/**
* Template Name: <PERSON><PERSON>
* Template URL: https://bootstrapmade.com/grandoria-bootstrap-hotel-template/
* Updated: Jul 29 2025 with Bootstrap v5.3.7
* Author: BootstrapMade.com
* License: https://bootstrapmade.com/license/
*/

@charset "UTF-8";

/*--------------------------------------------------------------
# Font & Color Variables
# Help: https://bootstrapmade.com/color-system/
--------------------------------------------------------------*/
/* Fonts */
:root {
  --default-font: "Roboto",  system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --heading-font: "Josefin Sans",  sans-serif;
  --nav-font: "Poppins",  sans-serif;
}

/* Global Colors - The following color variables are used throughout the website. Updating them here will change the color scheme of the entire website */
:root { 
  --background-color: #ffffff; /* Background color for the entire website, including individual sections */
  --default-color: #40524d; /* Default color used for the majority of the text content across the entire website */
  --heading-color: #0d1f1a; /* Color for headings, subheadings and title throughout the website */
  --accent-color: #2f5d50; /* Accent color that represents your brand on the website. It's used for buttons, links, and other elements that need to stand out */
  --surface-color: #ffffff; /* The surface color is used as a background of boxed elements within sections, such as cards, icon boxes, or other elements that require a visual separation from the global background. */
  --contrast-color: #ffffff; /* Contrast color for text, ensuring readability against backgrounds of accent, heading, or default colors. */
}

/* Nav Menu Colors - The following color variables are used specifically for the navigation menu. They are separate from the global colors to allow for more customization options */
:root {
  --nav-color: rgba(255, 255, 255, 0.6);  /* The default color of the main navmenu links */
  --nav-hover-color: #ffffff; /* Applied to main navmenu links when they are hovered over or active */
  --nav-mobile-background-color: #ffffff; /* Used as the background color for mobile navigation menu */
  --nav-dropdown-background-color: #ffffff; /* Used as the background color for dropdown items that appear when hovering over primary navigation items */
  --nav-dropdown-color: #40524d; /* Used for navigation links of the dropdown items in the navigation menu. */
  --nav-dropdown-hover-color: #2f5d50; /* Similar to --nav-hover-color, this color is applied to dropdown navigation links when they are hovered over. */
}

/* Color Presets - These classes override global colors when applied to any section or element, providing reuse of the sam color scheme. */

.light-background {
  --background-color: #f1f6f5;
  --surface-color: #ffffff;
}

.dark-background {
  --background-color: #2f5d50;
  --default-color: #ffffff;
  --heading-color: #ffffff;
  --surface-color: #2e2b28;
  --contrast-color: #ffffff;
}

/* Smooth scroll */
:root {
  scroll-behavior: smooth;
}

/*--------------------------------------------------------------
# General Styling & Shared Classes
--------------------------------------------------------------*/
body {
  color: var(--default-color);
  background-color: var(--background-color);
  font-family: var(--default-font);
}

a {
  color: var(--accent-color);
  text-decoration: none;
  transition: 0.3s;
}

a:hover {
  color: color-mix(in srgb, var(--accent-color), transparent 25%);
  text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--heading-color);
  font-family: var(--heading-font);
}

/* PHP Email Form Messages
------------------------------*/
.php-email-form .error-message {
  display: none;
  background: #df1529;
  color: #ffffff;
  text-align: left;
  padding: 15px;
  margin-bottom: 24px;
  font-weight: 600;
}

.php-email-form .sent-message {
  display: none;
  color: #ffffff;
  background: #059652;
  text-align: center;
  padding: 15px;
  margin-bottom: 24px;
  font-weight: 600;
}

.php-email-form .loading {
  display: none;
  background: var(--surface-color);
  text-align: center;
  padding: 15px;
  margin-bottom: 24px;
}

.php-email-form .loading:before {
  content: "";
  display: inline-block;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  margin: 0 10px -6px 0;
  border: 3px solid var(--accent-color);
  border-top-color: var(--surface-color);
  animation: php-email-form-loading 1s linear infinite;
}

@keyframes php-email-form-loading {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/*--------------------------------------------------------------
# Global Header
--------------------------------------------------------------*/
.header {
  --background-color: #436e62;
  --heading-color: #ffffff;
  color: var(--default-color);
  transition: all 0.5s;
  z-index: 997;
  background-color: var(--background-color);
}

.header .topbar {
  background-color: var(--background-color);
  height: 40px;
  padding: 0;
  font-size: 14px;
  transition: all 0.5s;
}

.header .topbar .contact-info i {
  font-style: normal;
  color: var(--contrast-color);
}

.header .topbar .contact-info i a,
.header .topbar .contact-info i span {
  padding-left: 5px;
  color: var(--contrast-color);
}

@media (max-width: 575px) {

  .header .topbar .contact-info i a,
  .header .topbar .contact-info i span {
    font-size: 13px;
  }
}

.header .topbar .contact-info i a {
  line-height: 0;
  transition: 0.3s;
}

.header .topbar .contact-info i a:hover {
  color: var(--contrast-color);
  text-decoration: underline;
}

.header .topbar .social-links a {
  color: color-mix(in srgb, var(--contrast-color), transparent 40%);
  line-height: 0;
  transition: 0.3s;
  margin-left: 20px;
}

.header .topbar .social-links a:hover {
  color: var(--contrast-color);
}

.header .branding {
  min-height: 60px;
  padding: 10px 0;
}

.header .logo {
  line-height: 1;
}

.header .logo img {
  max-height: 36px;
  margin-right: 8px;
}

.header .logo h1 {
  font-size: 30px;
  margin: 0;
  font-weight: 700;
  color: var(--heading-color);
}

.scrolled .header {
  box-shadow: 0px 0 18px rgba(0, 0, 0, 0.1);
}

.scrolled .header .topbar {
  height: 0;
  visibility: hidden;
  overflow: hidden;
}

/*--------------------------------------------------------------
# Navigation Menu
--------------------------------------------------------------*/
/* Navmenu - Desktop */
@media (min-width: 1200px) {
  .navmenu {
    padding: 0;
  }

  .navmenu svg {
    height: 16px;
    padding-right: 10px;
  }

  .navmenu ul {
    margin: 0;
    padding: 0;
    display: flex;
    list-style: none;
    align-items: center;
  }

  .navmenu li {
    position: relative;
  }

  .navmenu a,
  .navmenu a:focus {
    color: var(--nav-color);
    padding: 18px 15px;
    font-size: 15px;
    font-family: var(--nav-font);
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
    transition: 0.3s;
  }

  .navmenu a i,
  .navmenu a:focus i {
    font-size: 12px;
    line-height: 0;
    margin-left: 5px;
    transition: 0.3s;
  }

  .navmenu li:last-child a {
    padding-right: 0;
  }

  .navmenu li:hover>a,
  .navmenu .active,
  .navmenu .active:focus {
    color: var(--nav-hover-color);
  }

  .navmenu .dropdown ul {
    margin: 0;
    padding: 10px 0;
    background: var(--nav-dropdown-background-color);
    display: block;
    position: absolute;
    visibility: hidden;
    left: 14px;
    top: 130%;
    opacity: 0;
    transition: 0.3s;
    border-radius: 4px;
    z-index: 99;
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.1);
  }

  .navmenu .dropdown ul li {
    min-width: 200px;
  }

  .navmenu .dropdown ul a {
    padding: 10px 20px;
    font-size: 15px;
    text-transform: none;
    color: var(--nav-dropdown-color);
  }

  .navmenu .dropdown ul a i {
    font-size: 12px;
  }

  .navmenu .dropdown ul a:hover,
  .navmenu .dropdown ul .active,
  .navmenu .dropdown ul .active:hover,
  .navmenu .dropdown ul li:hover>a {
    color: var(--nav-dropdown-hover-color);
  }

  .navmenu .dropdown:hover>ul {
    opacity: 1;
    top: 100%;
    visibility: visible;
  }

  .navmenu .dropdown .dropdown ul {
    top: 0;
    left: -90%;
    visibility: hidden;
  }

  .navmenu .dropdown .dropdown:hover>ul {
    opacity: 1;
    top: 0;
    left: -100%;
    visibility: visible;
  }
}

/* Navmenu - Mobile */
@media (max-width: 1199px) {
  .mobile-nav-toggle {
    color: var(--nav-color);
    font-size: 28px;
    line-height: 0;
    margin-right: 10px;
    cursor: pointer;
    transition: color 0.3s;
  }

  .navmenu {
    padding: 0;
    z-index: 9997;
  }

  .navmenu svg {
    height: 16px;
    padding-right: 10px;
  }

  .navmenu ul {
    display: none;
    list-style: none;
    position: absolute;
    inset: 60px 20px 20px 20px;
    padding: 10px 0;
    margin: 0;
    border-radius: 6px;
    background-color: var(--nav-mobile-background-color);
    overflow-y: auto;
    transition: 0.3s;
    z-index: 9998;
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.1);
  }

  .navmenu a,
  .navmenu a:focus {
    color: var(--nav-dropdown-color);
    padding: 10px 20px;
    font-family: var(--nav-font);
    font-size: 17px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
    transition: 0.3s;
  }

  .navmenu a i,
  .navmenu a:focus i {
    font-size: 12px;
    line-height: 0;
    margin-left: 5px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: 0.3s;
    background-color: color-mix(in srgb, var(--accent-color), transparent 90%);
  }

  .navmenu a i:hover,
  .navmenu a:focus i:hover {
    background-color: var(--accent-color);
    color: var(--contrast-color);
  }

  .navmenu a:hover,
  .navmenu .active,
  .navmenu .active:focus {
    color: var(--nav-dropdown-hover-color);
  }

  .navmenu .active i,
  .navmenu .active:focus i {
    background-color: var(--accent-color);
    color: var(--contrast-color);
    transform: rotate(180deg);
  }

  .navmenu .dropdown ul {
    position: static;
    display: none;
    z-index: 99;
    padding: 10px 0;
    margin: 10px 20px;
    background-color: var(--nav-dropdown-background-color);
    border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
    box-shadow: none;
    transition: all 0.5s ease-in-out;
  }

  .navmenu .dropdown ul ul {
    background-color: rgba(33, 37, 41, 0.1);
  }

  .navmenu .dropdown>.dropdown-active {
    display: block;
    background-color: rgba(33, 37, 41, 0.03);
  }

  .mobile-nav-active {
    overflow: hidden;
  }

  .mobile-nav-active .mobile-nav-toggle {
    color: #fff;
    position: absolute;
    font-size: 32px;
    top: 15px;
    right: 15px;
    margin-right: 0;
    z-index: 9999;
  }

  .mobile-nav-active .navmenu {
    position: fixed;
    overflow: hidden;
    inset: 0;
    background: rgba(33, 37, 41, 0.8);
    transition: 0.3s;
  }

  .mobile-nav-active .navmenu>ul {
    display: block;
  }
}

/*--------------------------------------------------------------
# Global Footer
--------------------------------------------------------------*/
.footer {
  color: var(--default-color);
  background-color: var(--background-color);
  font-size: 14px;
  position: relative;
}

.footer .footer-top {
  background-color: color-mix(in srgb, var(--background-color), white 5%);
  padding-top: 50px;
}

.footer .footer-about .logo {
  line-height: 1;
  margin-bottom: 25px;
}

.footer .footer-about .logo img {
  max-height: 40px;
  margin-right: 6px;
}

.footer .footer-about .logo span {
  color: var(--heading-color);
  font-family: var(--heading-font);
  font-size: 26px;
  font-weight: 700;
  letter-spacing: 1px;
}

.footer .footer-about p {
  font-size: 14px;
  font-family: var(--heading-font);
}

.footer h4 {
  font-size: 16px;
  font-weight: 600;
  position: relative;
  padding-bottom: 12px;
  margin-bottom: 15px;
}

.footer h4::after {
  content: "";
  position: absolute;
  display: block;
  width: 20px;
  height: 2px;
  background: var(--accent-color);
  bottom: 0;
  left: 0;
}

.footer .footer-links {
  margin-bottom: 30px;
}

.footer .footer-links ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer .footer-links ul i {
  padding-right: 2px;
  font-size: 12px;
  line-height: 0;
}

.footer .footer-links ul li {
  padding: 10px 0;
  display: flex;
  align-items: center;
}

.footer .footer-links ul li:first-child {
  padding-top: 0;
}

.footer .footer-links ul a {
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  display: inline-block;
  line-height: 1;
}

.footer .footer-links ul a:hover {
  color: var(--accent-color);
}

.footer .footer-contact p {
  margin-bottom: 5px;
}

.footer .copyright {
  padding: 30px 0;
}

.footer .copyright p {
  margin-bottom: 0;
}

.footer .credits {
  margin-top: 5px;
  font-size: 13px;
}

.footer .social-links a {
  font-size: 18px;
  display: inline-block;
  background: color-mix(in srgb, var(--default-color), transparent 92%);
  color: var(--accent-color);
  line-height: 1;
  padding: 8px 0;
  margin-right: 4px;
  border-radius: 4px;
  text-align: center;
  width: 36px;
  height: 36px;
  transition: 0.3s;
}

.footer .social-links a:hover {
  color: var(--contrast-color);
  background: var(--accent-color);
  text-decoration: none;
}

/*--------------------------------------------------------------
# Preloader
--------------------------------------------------------------*/
#preloader {
  position: fixed;
  inset: 0;
  z-index: 999999;
  overflow: hidden;
  background: var(--background-color);
  transition: all 0.6s ease-out;
}

#preloader:before {
  content: "";
  position: fixed;
  top: calc(50% - 30px);
  left: calc(50% - 30px);
  border: 6px solid #ffffff;
  border-color: var(--accent-color) transparent var(--accent-color) transparent;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  animation: animate-preloader 1.5s linear infinite;
}

@keyframes animate-preloader {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/*--------------------------------------------------------------
# Scroll Top Button
--------------------------------------------------------------*/
.scroll-top {
  position: fixed;
  visibility: hidden;
  opacity: 0;
  right: 15px;
  bottom: -15px;
  z-index: 99999;
  background-color: var(--accent-color);
  width: 44px;
  height: 44px;
  border-radius: 50px;
  transition: all 0.4s;
}

.scroll-top i {
  font-size: 24px;
  color: var(--contrast-color);
  line-height: 0;
}

.scroll-top:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 20%);
  color: var(--contrast-color);
}

.scroll-top.active {
  visibility: visible;
  opacity: 1;
  bottom: 15px;
}

/*--------------------------------------------------------------
# Disable aos animation delay on mobile devices
--------------------------------------------------------------*/
@media screen and (max-width: 768px) {
  [data-aos-delay] {
    transition-delay: 0 !important;
  }
}

/*--------------------------------------------------------------
# Global Page Titles & Breadcrumbs
--------------------------------------------------------------*/
.page-title {
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 25px 0;
  position: relative;
}

.page-title h1 {
  font-size: 24px;
  font-weight: 700;
}

.page-title .breadcrumbs ol {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  padding: 0;
  margin: 0;
  font-size: 14px;
  font-weight: 400;
}

.page-title .breadcrumbs ol li+li {
  padding-left: 10px;
}

.page-title .breadcrumbs ol li+li::before {
  content: "/";
  display: inline-block;
  padding-right: 10px;
  color: color-mix(in srgb, var(--default-color), transparent 70%);
}

/*--------------------------------------------------------------
# Global Sections
--------------------------------------------------------------*/
section,
.section {
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 60px 0;
  scroll-margin-top: 90px;
  overflow: clip;
}

@media (max-width: 1199px) {

  section,
  .section {
    scroll-margin-top: 66px;
  }
}

/*--------------------------------------------------------------
# Global Section Titles
--------------------------------------------------------------*/
.section-title {
  text-align: center;
  padding: 30px 0;
  margin-bottom: 30px;
  position: relative;
}

.section-title h2 {
  font-size: 32px;
  font-weight: 700;
  text-transform: uppercase;
  margin-bottom: 20px;
  padding-bottom: 0;
  position: relative;
  z-index: 2;
}

.section-title span {
  position: absolute;
  top: 4px;
  color: color-mix(in srgb, var(--heading-color), transparent 95%);
  left: 0;
  right: 0;
  z-index: 1;
  font-weight: 700;
  font-size: 52px;
  text-transform: uppercase;
  line-height: 1;
}

.section-title p {
  margin-bottom: 0;
  position: relative;
  z-index: 2;
}

@media (max-width: 575px) {
  .section-title h2 {
    font-size: 28px;
    margin-bottom: 15px;
  }

  .section-title span {
    font-size: 38px;
  }
}

/*--------------------------------------------------------------
# Hotel Hero Section
--------------------------------------------------------------*/
.hotel-hero {
  padding-top: 60px;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  padding-bottom: 60px;
}

.hotel-hero .hero-content {
  position: relative;
  z-index: 2;
}

.hotel-hero .hero-content h1 {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  color: var(--heading-color);
}

@media (max-width: 992px) {
  .hotel-hero .hero-content h1 {
    font-size: 2.8rem;
  }
}

@media (max-width: 768px) {
  .hotel-hero .hero-content h1 {
    font-size: 2.2rem;
  }
}

.hotel-hero .hero-content .lead {
  font-size: 1.2rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
}

@media (max-width: 768px) {
  .hotel-hero .hero-content .lead {
    font-size: 1.1rem;
  }
}

.hotel-hero .hero-features {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  margin-bottom: 2.5rem;
}

@media (max-width: 576px) {
  .hotel-hero .hero-features {
    flex-direction: column;
    gap: 1rem;
  }
}

.hotel-hero .hero-features .feature-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: var(--default-color);
}

.hotel-hero .hero-features .feature-item i {
  color: var(--accent-color);
  font-size: 1.2rem;
}

.hotel-hero .hero-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.hotel-hero .hero-buttons .btn {
  padding: 1rem 2rem;
  font-weight: 600;
  text-decoration: none;
  border-radius: 50px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.hotel-hero .hero-buttons .btn.btn-primary {
  background-color: var(--accent-color);
  color: var(--contrast-color);
  border-color: var(--accent-color);
}

.hotel-hero .hero-buttons .btn.btn-primary:hover {
  background-color: color-mix(in srgb, var(--accent-color), black 10%);
  border-color: color-mix(in srgb, var(--accent-color), black 10%);
  transform: translateY(-2px);
}

.hotel-hero .hero-buttons .btn.btn-outline {
  background-color: transparent;
  color: var(--accent-color);
  border-color: var(--accent-color);
}

.hotel-hero .hero-buttons .btn.btn-outline:hover {
  transform: translateY(-2px);
}

.hotel-hero .hero-images {
  position: relative;
  z-index: 2;
}

.hotel-hero .hero-images .main-image {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 60px color-mix(in srgb, var(--default-color), transparent 85%);
}

.hotel-hero .hero-images .main-image img {
  width: 100%;
  height: 500px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

@media (max-width: 768px) {
  .hotel-hero .hero-images .main-image img {
    height: 400px;
  }
}

.hotel-hero .hero-images .main-image:hover img {
  transform: scale(1.05);
}

.hotel-hero .hero-images .floating-card {
  position: absolute;
  bottom: -30px;
  right: -30px;
  background: var(--surface-color);
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 15px 40px color-mix(in srgb, var(--default-color), transparent 85%);
  max-width: 280px;
  z-index: 10;
}

@media (max-width: 992px) {
  .hotel-hero .hero-images .floating-card {
    position: relative;
    bottom: auto;
    right: auto;
    margin-top: 2rem;
    max-width: none;
  }
}

.hotel-hero .hero-images .floating-card .rating {
  margin-bottom: 0.5rem;
}

.hotel-hero .hero-images .floating-card .rating i {
  color: #ffc107;
  margin-right: 0.2rem;
}

.hotel-hero .hero-images .floating-card h6 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--heading-color);
}

.hotel-hero .hero-images .floating-card p {
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 1rem;
  color: var(--default-color);
}

.hotel-hero .hero-images .floating-card .guest-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.hotel-hero .hero-images .floating-card .guest-info .guest-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.hotel-hero .hero-images .floating-card .guest-info span {
  font-weight: 500;
  color: var(--heading-color);
}

.hotel-hero .hero-stats {
  margin-top: 4rem;
  padding: 2rem 0;
  border-top: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
}

.hotel-hero .hero-stats .stat-item .stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--accent-color);
  line-height: 1;
  margin-bottom: 0.5rem;
}

@media (max-width: 768px) {
  .hotel-hero .hero-stats .stat-item .stat-number {
    font-size: 2rem;
  }
}

.hotel-hero .hero-stats .stat-item .stat-label {
  display: block;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--default-color);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

@media (max-width: 992px) {
  .hotel-hero {
    min-height: auto;
    padding: 4rem 0;
  }

  .hotel-hero .row {
    min-height: auto;
  }

  .hotel-hero::before {
    display: none;
  }
}

/*--------------------------------------------------------------
# About Home Section
--------------------------------------------------------------*/
.about-home .about-content h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  font-weight: 700;
}

.about-home .about-content .lead {
  font-size: 1.25rem;
  color: var(--accent-color);
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.about-home .about-content p {
  margin-bottom: 1.25rem;
  line-height: 1.7;
}

.about-home .about-content .stats-row {
  display: flex;
  gap: 2rem;
  margin: 2rem 0;
}

@media (max-width: 576px) {
  .about-home .about-content .stats-row {
    flex-direction: column;
    gap: 1rem;
  }
}

.about-home .about-content .stats-row .stat-item {
  text-align: center;
}

.about-home .about-content .stats-row .stat-item .stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--accent-color);
  font-family: var(--heading-font);
}

.about-home .about-content .stats-row .stat-item .stat-label {
  font-size: 0.875rem;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  margin-top: 0.25rem;
}

.about-home .about-content .about-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

@media (max-width: 576px) {
  .about-home .about-content .about-actions {
    flex-direction: column;
  }
}

.about-home .about-content .about-actions .btn-primary,
.about-home .about-content .about-actions .btn-secondary {
  padding: 12px 24px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.about-home .about-content .about-actions .btn-primary {
  background-color: var(--accent-color);
  color: var(--contrast-color);
  border-color: var(--accent-color);
}

.about-home .about-content .about-actions .btn-primary:hover {
  background-color: color-mix(in srgb, var(--accent-color), black 15%);
  color: var(--contrast-color);
  transform: translateY(-2px);
}

.about-home .about-content .about-actions .btn-secondary {
  background-color: transparent;
  color: var(--heading-color);
  border-color: color-mix(in srgb, var(--default-color), transparent 70%);
}

.about-home .about-content .about-actions .btn-secondary:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 90%);
  border-color: var(--accent-color);
  color: var(--accent-color);
  transform: translateY(-2px);
}

.about-home .about-images {
  position: relative;
  height: 500px;
}

.about-home .about-images .main-image {
  position: absolute;
  top: 0;
  right: 0;
  width: 75%;
  height: 70%;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.about-home .about-images .main-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.about-home .about-images .secondary-image {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60%;
  height: 50%;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
  z-index: 2;
}

.about-home .about-images .secondary-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.about-home .about-images .experience-badge {
  position: absolute;
  top: 50%;
  right: 10%;
  transform: translateY(-50%);
  z-index: 3;
}

.about-home .about-images .experience-badge .badge-content {
  background-color: var(--accent-color);
  color: var(--contrast-color);
  padding: 1.5rem;
  border-radius: 50%;
  width: 100px;
  height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.about-home .about-images .experience-badge .badge-content .badge-number {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
}

.about-home .about-images .experience-badge .badge-content .badge-text {
  font-size: 0.75rem;
  line-height: 1.2;
  margin-top: 0.25rem;
}

@media (max-width: 768px) {
  .about-home .about-images {
    height: 400px;
    margin-top: 2rem;
  }

  .about-home .about-images .main-image {
    width: 80%;
    height: 65%;
  }

  .about-home .about-images .secondary-image {
    width: 65%;
    height: 45%;
  }

  .about-home .about-images .experience-badge {
    right: 5%;
  }

  .about-home .about-images .experience-badge .badge-content {
    width: 80px;
    height: 80px;
    padding: 1rem;
  }

  .about-home .about-images .experience-badge .badge-content .badge-number {
    font-size: 1.25rem;
  }

  .about-home .about-images .experience-badge .badge-content .badge-text {
    font-size: 0.6rem;
  }
}

@media (max-width: 992px) {
  .about-home .row {
    flex-direction: column-reverse;
  }

  .about-home .about-content {
    margin-top: 2rem;
    text-align: center;
  }

  .about-home .about-content .stats-row {
    justify-content: center;
  }
}

/*--------------------------------------------------------------
# Rooms Showcase Section
--------------------------------------------------------------*/
.rooms-showcase .mt-6 {
  margin-top: 5rem;
}

.rooms-showcase .hero-room-showcase {
  background-color: var(--surface-color);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.04);
  transition: all 0.5s ease;
  height: 100%;
}

.rooms-showcase .hero-room-showcase:hover {
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.08);
}

.rooms-showcase .hero-room-showcase .showcase-image-container {
  position: relative;
  height: 400px;
  overflow: hidden;
}

.rooms-showcase .hero-room-showcase .showcase-image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.6s ease;
}

.rooms-showcase .hero-room-showcase .showcase-image-container:hover img {
  transform: scale(1.03);
}

.rooms-showcase .hero-room-showcase .showcase-image-container .room-category-badge {
  position: absolute;
  top: 24px;
  left: 24px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--heading-color);
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.rooms-showcase .hero-room-showcase .showcase-image-container .room-details-overlay {
  position: absolute;
  bottom: 24px;
  left: 24px;
  right: 24px;
}

.rooms-showcase .hero-room-showcase .showcase-image-container .room-details-overlay .room-specs {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.rooms-showcase .hero-room-showcase .showcase-image-container .room-details-overlay .room-specs .spec-item {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 8px 16px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--heading-color);
}

.rooms-showcase .hero-room-showcase .showcase-image-container .room-details-overlay .room-specs .spec-item i {
  color: var(--accent-color);
  font-size: 0.9rem;
}

.rooms-showcase .hero-room-showcase .showcase-content {
  padding: 40px;
}

.rooms-showcase .hero-room-showcase .showcase-content .room-title-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.rooms-showcase .hero-room-showcase .showcase-content .room-title-section h2 {
  color: var(--heading-color);
  font-size: 1.8rem;
  font-weight: 300;
  letter-spacing: -0.5px;
  margin: 0;
  line-height: 1.2;
}

.rooms-showcase .hero-room-showcase .showcase-content .room-title-section .room-rating {
  text-align: right;
  white-space: nowrap;
}

.rooms-showcase .hero-room-showcase .showcase-content .room-title-section .room-rating .stars {
  color: #ffc107;
  font-size: 0.85rem;
  margin-bottom: 4px;
}

.rooms-showcase .hero-room-showcase .showcase-content .room-title-section .room-rating .rating-text {
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  font-size: 0.75rem;
  font-weight: 500;
}

.rooms-showcase .hero-room-showcase .showcase-content .room-description {
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  line-height: 1.7;
  margin-bottom: 32px;
  font-size: 0.95rem;
}

.rooms-showcase .hero-room-showcase .showcase-content .amenities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 16px;
  margin-bottom: 40px;
}

.rooms-showcase .hero-room-showcase .showcase-content .amenities-grid .amenity-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 0;
}

.rooms-showcase .hero-room-showcase .showcase-content .amenities-grid .amenity-item i {
  color: var(--accent-color);
  font-size: 1.1rem;
  width: 20px;
}

.rooms-showcase .hero-room-showcase .showcase-content .amenities-grid .amenity-item span {
  color: color-mix(in srgb, var(--default-color), transparent 25%);
  font-size: 0.85rem;
  font-weight: 500;
}

.rooms-showcase .hero-room-showcase .showcase-content .booking-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rooms-showcase .hero-room-showcase .showcase-content .booking-section .price-display {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.rooms-showcase .hero-room-showcase .showcase-content .booking-section .price-display .currency {
  color: var(--accent-color);
  font-size: 1.2rem;
  font-weight: 400;
}

.rooms-showcase .hero-room-showcase .showcase-content .booking-section .price-display .amount {
  color: var(--accent-color);
  font-size: 2.5rem;
  font-weight: 200;
  letter-spacing: -1px;
}

.rooms-showcase .hero-room-showcase .showcase-content .booking-section .price-display .period {
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  font-size: 0.9rem;
  margin-left: 8px;
}

.rooms-showcase .hero-room-showcase .showcase-content .booking-section .primary-booking-btn {
  background-color: var(--accent-color);
  color: var(--contrast-color);
  padding: 14px 32px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 400;
  font-size: 0.95rem;
  letter-spacing: 0.3px;
  transition: all 0.4s ease;
}

.rooms-showcase .hero-room-showcase .showcase-content .booking-section .primary-booking-btn:hover {
  background-color: color-mix(in srgb, var(--accent-color), black 15%);
  color: var(--contrast-color);
  transform: translateY(-1px);
}

.rooms-showcase .room-list-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  height: 100%;
}

.rooms-showcase .standard-room-card {
  background-color: var(--surface-color);
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  transition: all 0.4s ease;
  box-shadow: 0 1px 12px rgba(0, 0, 0, 0.04);
}

.rooms-showcase .standard-room-card:hover {
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  transform: translateX(4px);
}

.rooms-showcase .standard-room-card .card-image {
  position: relative;
  width: 120px;
  flex-shrink: 0;
}

.rooms-showcase .standard-room-card .card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.rooms-showcase .standard-room-card .card-image .view-link {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 32px;
  height: 32px;
  background-color: rgba(255, 255, 255, 0.9);
  color: var(--accent-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  opacity: 0;
  transition: all 0.3s ease;
}

.rooms-showcase .standard-room-card .card-image:hover .view-link {
  opacity: 1;
}

.rooms-showcase .standard-room-card .card-content {
  padding: 20px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.rooms-showcase .standard-room-card .card-content h4 {
  color: var(--heading-color);
  font-size: 1rem;
  font-weight: 400;
  margin-bottom: 8px;
  line-height: 1.3;
}

.rooms-showcase .standard-room-card .card-content p {
  color: color-mix(in srgb, var(--default-color), transparent 35%);
  font-size: 0.8rem;
  line-height: 1.5;
  margin-bottom: 12px;
  flex-grow: 1;
}

.rooms-showcase .standard-room-card .card-content .features-list {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.rooms-showcase .standard-room-card .card-content .features-list span {
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 4px;
}

.rooms-showcase .standard-room-card .card-content .features-list span i {
  color: var(--accent-color);
  font-size: 0.8rem;
}

.rooms-showcase .standard-room-card .card-content .booking-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rooms-showcase .standard-room-card .card-content .booking-row .price {
  color: var(--accent-color);
  font-size: 1.1rem;
  font-weight: 500;
}

.rooms-showcase .standard-room-card .card-content .booking-row .price small {
  color: color-mix(in srgb, var(--default-color), transparent 50%);
  font-size: 0.65rem;
  font-weight: 400;
}

.rooms-showcase .standard-room-card .card-content .booking-row .book-link {
  color: var(--accent-color);
  text-decoration: none;
  font-size: 0.8rem;
  font-weight: 500;
  padding: 6px 16px;
  border: 1px solid color-mix(in srgb, var(--accent-color), transparent 70%);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.rooms-showcase .standard-room-card .card-content .booking-row .book-link:hover {
  background-color: var(--accent-color);
  color: var(--contrast-color);
  border-color: var(--accent-color);
}

.rooms-showcase .minimal-room-card {
  background-color: var(--surface-color);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.4s ease;
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.03);
}

.rooms-showcase .minimal-room-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  transform: translateY(-2px);
}

.rooms-showcase .minimal-room-card .room-image {
  position: relative;
  height: 180px;
  overflow: hidden;
}

.rooms-showcase .minimal-room-card .room-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.rooms-showcase .minimal-room-card .room-image .hover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(54, 144, 231, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.rooms-showcase .minimal-room-card .room-image .hover-overlay .view-room {
  color: var(--contrast-color);
  font-size: 1.4rem;
  text-decoration: none;
  transition: transform 0.3s ease;
}

.rooms-showcase .minimal-room-card .room-image .hover-overlay .view-room:hover {
  color: var(--contrast-color);
  transform: scale(1.1);
}

.rooms-showcase .minimal-room-card .room-image:hover .hover-overlay {
  opacity: 1;
}

.rooms-showcase .minimal-room-card .room-image:hover img {
  transform: scale(1.04);
}

.rooms-showcase .minimal-room-card .room-summary {
  padding: 24px 20px;
  text-align: center;
}

.rooms-showcase .minimal-room-card .room-summary h5 {
  color: var(--heading-color);
  font-size: 0.95rem;
  font-weight: 400;
  margin-bottom: 12px;
}

.rooms-showcase .minimal-room-card .room-summary .price-tag {
  color: var(--accent-color);
  font-size: 1.2rem;
  font-weight: 300;
  margin-bottom: 16px;
  display: block;
}

.rooms-showcase .minimal-room-card .room-summary .price-tag span {
  color: color-mix(in srgb, var(--default-color), transparent 50%);
  font-size: 0.7rem;
  font-weight: 400;
}

.rooms-showcase .minimal-room-card .room-summary .basic-amenities {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.rooms-showcase .minimal-room-card .room-summary .basic-amenities i {
  color: var(--accent-color);
  font-size: 1rem;
  opacity: 0.8;
}

.rooms-showcase .explore-all-link {
  color: var(--heading-color);
  text-decoration: none;
  font-size: 0.95rem;
  font-weight: 400;
  letter-spacing: 0.3px;
  padding: 16px 0;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  border-bottom: 1px solid transparent;
  transition: all 0.4s ease;
}

.rooms-showcase .explore-all-link:hover {
  color: var(--accent-color);
  border-bottom-color: var(--accent-color);
  transform: translateX(4px);
}

.rooms-showcase .explore-all-link i {
  font-size: 0.85rem;
  transition: transform 0.3s ease;
}

.rooms-showcase .explore-all-link:hover i {
  transform: translateX(4px);
}

@media (max-width: 1200px) {
  .rooms-showcase .hero-room-showcase .showcase-content .amenities-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 992px) {
  .rooms-showcase .standard-room-card {
    flex-direction: column;
  }

  .rooms-showcase .standard-room-card .card-image {
    width: 100%;
    height: 120px;
  }

  .rooms-showcase .hero-room-showcase .showcase-content .room-title-section {
    flex-direction: column;
    gap: 12px;
  }

  .rooms-showcase .hero-room-showcase .showcase-content .room-title-section .room-rating {
    text-align: left;
  }

  .rooms-showcase .hero-room-showcase .showcase-content .booking-section {
    flex-direction: column;
    gap: 20px;
    align-items: center;
  }
}

@media (max-width: 768px) {
  .rooms-showcase .mt-6 {
    margin-top: 3rem;
  }

  .rooms-showcase .hero-room-showcase .showcase-image-container {
    height: 280px;
  }

  .rooms-showcase .hero-room-showcase .showcase-image-container .room-details-overlay .room-specs {
    gap: 8px;
  }

  .rooms-showcase .hero-room-showcase .showcase-image-container .room-details-overlay .room-specs .spec-item {
    padding: 6px 12px;
    font-size: 0.75rem;
  }

  .rooms-showcase .hero-room-showcase .showcase-content {
    padding: 24px;
  }

  .rooms-showcase .hero-room-showcase .showcase-content .room-title-section h2 {
    font-size: 1.4rem;
  }

  .rooms-showcase .hero-room-showcase .showcase-content .amenities-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

/*--------------------------------------------------------------
# Amenities Cards Section
--------------------------------------------------------------*/
.amenities-cards .facility-card {
  background-color: var(--surface-color);
  border-radius: 16px;
  height: 100%;
  overflow: hidden;
  box-shadow: 0 5px 25px color-mix(in srgb, var(--default-color), transparent 88%);
  transition: all 0.4s ease;
  position: relative;
}

.amenities-cards .facility-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 40px color-mix(in srgb, var(--default-color), transparent 80%);
}

.amenities-cards .facility-card:hover .facility-image .facility-overlay {
  background: color-mix(in srgb, var(--accent-color), transparent 15%);
}

.amenities-cards .facility-card:hover .facility-image img {
  transform: scale(1.05);
}

.amenities-cards .facility-image {
  position: relative;
  height: 220px;
  overflow: hidden;
}

.amenities-cards .facility-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.amenities-cards .facility-image .facility-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: color-mix(in srgb, var(--default-color), transparent 30%);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s ease;
}

.amenities-cards .facility-image .facility-overlay i {
  font-size: 48px;
  color: var(--contrast-color);
}

.amenities-cards .facility-info {
  padding: 30px 25px;
}

.amenities-cards .facility-info h4 {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 15px;
  color: var(--heading-color);
}

.amenities-cards .facility-info p {
  color: color-mix(in srgb, var(--default-color), transparent 15%);
  font-size: 15px;
  line-height: 1.7;
  margin-bottom: 20px;
}

.amenities-cards .facility-features {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.amenities-cards .facility-features span {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  font-weight: 500;
  color: var(--accent-color);
}

.amenities-cards .facility-features span i {
  font-size: 14px;
}

@media (max-width: 992px) {
  .amenities-cards .facility-image {
    height: 200px;
  }

  .amenities-cards .facility-info {
    padding: 25px 20px;
  }

  .amenities-cards .facility-info h4 {
    font-size: 20px;
    margin-bottom: 12px;
  }

  .amenities-cards .facility-info p {
    font-size: 14px;
    margin-bottom: 18px;
  }
}

@media (max-width: 768px) {
  .amenities-cards .facility-image {
    height: 180px;
  }

  .amenities-cards .facility-image .facility-overlay i {
    font-size: 40px;
  }

  .amenities-cards .facility-info {
    padding: 22px 18px;
  }

  .amenities-cards .facility-info h4 {
    font-size: 18px;
  }

  .amenities-cards .facility-info p {
    font-size: 13px;
    line-height: 1.6;
  }

  .amenities-cards .facility-features {
    gap: 12px;
  }

  .amenities-cards .facility-features span {
    font-size: 12px;
  }
}

/*--------------------------------------------------------------
# Testimonials Section
--------------------------------------------------------------*/
.testimonials {
  /* Responsive Styles */
}

.testimonials .testimonials-wrapper {
  padding: 20px 0;
}

.testimonials .testimonial-card {
  position: relative;
  background-color: var(--surface-color);
  border-radius: 12px;
  padding: 40px 30px 30px;
  height: 100%;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.03);
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 92%);
  transition: all 0.3s ease;
  overflow: hidden;
}

.testimonials .testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.06);
  border-color: color-mix(in srgb, var(--accent-color), transparent 80%);
}

.testimonials .testimonial-card:hover .quote-mark {
  color: var(--accent-color);
}

.testimonials .quote-mark {
  position: absolute;
  top: 20px;
  left: 25px;
  font-size: 2.5rem;
  line-height: 1;
  color: color-mix(in srgb, var(--accent-color), transparent 80%);
  transition: color 0.3s ease;
  opacity: 0.8;
}

.testimonials .quote-mark i {
  transform: scaleX(-1);
}

.testimonials .testimonial-content {
  margin-bottom: 25px;
  padding-top: 10px;
}

.testimonials .testimonial-content p {
  font-size: 1rem;
  line-height: 1.7;
  color: color-mix(in srgb, var(--default-color), transparent 15%);
  font-style: italic;
  margin-bottom: 0;
}

.testimonials .testimonial-author {
  display: flex;
  align-items: center;
}

.testimonials .testimonial-author img {
  width: 55px;
  height: 55px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid color-mix(in srgb, var(--accent-color), transparent 85%);
  margin-right: 15px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
}

.testimonials .testimonial-author .author-info h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 5px;
  color: var(--heading-color);
}

.testimonials .testimonial-author .author-info span {
  font-size: 0.85rem;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  display: block;
}

@media (max-width: 1199px) {
  .testimonials .testimonial-card {
    padding: 35px 25px 25px;
  }

  .testimonials .quote-mark {
    font-size: 2.2rem;
  }
}

@media (max-width: 991px) {
  .testimonials .testimonial-card {
    margin-bottom: 20px;
  }

  .testimonials .testimonial-content p {
    font-size: 0.95rem;
  }
}

@media (max-width: 767px) {
  .testimonials .testimonial-card {
    padding: 30px 20px 20px;
  }

  .testimonials .quote-mark {
    font-size: 2rem;
    top: 15px;
    left: 20px;
  }

  .testimonials .testimonial-author img {
    width: 50px;
    height: 50px;
  }

  .testimonials .testimonial-author .author-info h4 {
    font-size: 1rem;
  }

  .testimonials .testimonial-author .author-info span {
    font-size: 0.8rem;
  }
}

@media (max-width: 575px) {
  .testimonials .testimonial-card {
    padding: 25px 18px 18px;
  }

  .testimonials .quote-mark {
    font-size: 1.8rem;
  }

  .testimonials .testimonial-content p {
    font-size: 0.9rem;
  }

  .testimonials .testimonial-author img {
    width: 45px;
    height: 45px;
    margin-right: 12px;
  }
}

/*--------------------------------------------------------------
# Offer Cards Section
--------------------------------------------------------------*/
.offer-cards .offer-card {
  background: var(--surface-color);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.offer-cards .offer-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.offer-cards .offer-card.featured {
  border: 2px solid var(--accent-color);
}

.offer-cards .offer-card.featured .offer-badge .discount {
  background: linear-gradient(135deg, var(--accent-color) 0%, color-mix(in srgb, var(--accent-color), #000 20%) 100%);
}

.offer-cards .offer-card.featured .btn-book {
  background: var(--accent-color);
  color: var(--contrast-color);
}

.offer-cards .offer-card.featured .btn-book:hover {
  background: color-mix(in srgb, var(--accent-color), #000 10%);
}

.offer-cards .offer-card .offer-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  z-index: 2;
}

.offer-cards .offer-card .offer-badge .discount {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: var(--contrast-color);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.offer-cards .offer-card .offer-image {
  position: relative;
  height: 220px;
  overflow: hidden;
}

.offer-cards .offer-card .offer-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.offer-cards .offer-card:hover .offer-image img {
  transform: scale(1.05);
}

.offer-cards .offer-card .offer-content {
  padding: 25px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.offer-cards .offer-card .offer-content h3 {
  color: var(--heading-color);
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 12px;
  line-height: 1.3;
}

.offer-cards .offer-card .offer-content p {
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 20px;
  flex: 1;
}

.offer-cards .offer-card .offer-content .offer-details {
  margin-bottom: 20px;
}

.offer-cards .offer-card .offer-content .offer-details .price-info {
  display: flex;
  align-items: baseline;
  gap: 8px;
  margin-bottom: 10px;
}

.offer-cards .offer-card .offer-content .offer-details .price-info .original-price {
  color: color-mix(in srgb, var(--default-color), transparent 50%);
  text-decoration: line-through;
  font-size: 14px;
}

.offer-cards .offer-card .offer-content .offer-details .price-info .offer-price {
  color: var(--accent-color);
  font-size: 24px;
  font-weight: 700;
}

.offer-cards .offer-card .offer-content .offer-details .price-info .per-night {
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  font-size: 12px;
}

.offer-cards .offer-card .offer-content .offer-details .validity {
  display: flex;
  align-items: center;
  gap: 6px;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  font-size: 13px;
}

.offer-cards .offer-card .offer-content .offer-details .validity i {
  color: var(--accent-color);
}

.offer-cards .offer-card .offer-content .btn-book {
  background: var(--accent-color);
  color: var(--contrast-color);
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  text-decoration: none;
  text-align: center;
  transition: all 0.3s ease;
  display: inline-block;
}

.offer-cards .offer-card .offer-content .btn-book:hover {
  background: color-mix(in srgb, var(--accent-color), #000 10%);
  color: var(--contrast-color);
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .offer-cards .offer-card .offer-content {
    padding: 20px;
  }
}

.offer-cards .offer-banner {
  background: linear-gradient(135deg, var(--accent-color) 0%, color-mix(in srgb, var(--accent-color), #000 30%) 100%);
  color: var(--contrast-color);
  padding: 40px;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.offer-cards .offer-banner::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: color-mix(in srgb, var(--contrast-color), transparent 90%);
  border-radius: 50%;
  transform: translate(50%, -50%);
}

.offer-cards .offer-banner .banner-content {
  position: relative;
  z-index: 2;
}

.offer-cards .offer-banner .banner-content h2 {
  color: var(--contrast-color);
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 15px;
}

.offer-cards .offer-banner .banner-content p {
  color: color-mix(in srgb, var(--contrast-color), transparent 10%);
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 0;
}

.offer-cards .offer-banner .banner-action {
  position: relative;
  z-index: 2;
  text-align: center;
}

.offer-cards .offer-banner .banner-action .countdown {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 20px;
}

.offer-cards .offer-banner .banner-action .countdown div {
  text-align: center;
}

.offer-cards .offer-banner .banner-action .countdown div span {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: var(--contrast-color);
  line-height: 1;
}

.offer-cards .offer-banner .banner-action .countdown div label {
  font-size: 12px;
  color: color-mix(in srgb, var(--contrast-color), transparent 20%);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 4px;
}

.offer-cards .offer-banner .banner-action .btn-cta {
  background: var(--contrast-color);
  color: var(--accent-color);
  padding: 15px 30px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 16px;
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-block;
}

.offer-cards .offer-banner .banner-action .btn-cta:hover {
  background: color-mix(in srgb, var(--contrast-color), transparent 10%);
  color: var(--accent-color);
  transform: translateY(-2px);
}

@media (max-width: 992px) {
  .offer-cards .offer-banner {
    text-align: center;
  }

  .offer-cards .offer-banner .banner-action {
    margin-top: 30px;
  }
}

@media (max-width: 768px) {
  .offer-cards .offer-banner {
    padding: 30px 20px;
  }

  .offer-cards .offer-banner .banner-content h2 {
    font-size: 24px;
  }

  .offer-cards .offer-banner .banner-action .countdown {
    gap: 15px;
  }

  .offer-cards .offer-banner .banner-action .countdown div span {
    font-size: 20px;
  }
}

/*--------------------------------------------------------------
# Call To Action Section
--------------------------------------------------------------*/
.call-to-action {
  position: relative;
  overflow: hidden;
}

.call-to-action .container {
  position: relative;
  z-index: 1;
}

.call-to-action .content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  color: var(--heading-color);
}

@media (max-width: 768px) {
  .call-to-action .content h2 {
    font-size: 2rem;
  }
}

.call-to-action .content p {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
}

.call-to-action .features-list {
  list-style: none;
  padding: 0;
  margin: 0 0 2.5rem 0;
}

.call-to-action .features-list li {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  font-size: 1rem;
  color: var(--default-color);
}

.call-to-action .features-list li i {
  color: var(--accent-color);
  font-size: 1.2rem;
  margin-right: 1rem;
  flex-shrink: 0;
}

.call-to-action .cta-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

@media (max-width: 576px) {
  .call-to-action .cta-buttons {
    flex-direction: column;
  }
}

.call-to-action .btn-primary {
  background: var(--accent-color);
  color: var(--contrast-color);
  padding: 15px 30px;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px color-mix(in srgb, var(--accent-color), transparent 70%);
}

.call-to-action .btn-primary:hover {
  background: color-mix(in srgb, var(--accent-color), black 10%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px color-mix(in srgb, var(--accent-color), transparent 60%);
  color: var(--contrast-color);
}

.call-to-action .btn-secondary {
  background: transparent;
  color: var(--default-color);
  border: 2px solid var(--default-color);
  padding: 13px 28px;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.call-to-action .btn-secondary:hover {
  background: var(--accent-color);
  border-color: var(--accent-color);
  color: var(--contrast-color);
  transform: translateY(-2px);
}

.call-to-action .image-wrapper {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 15px 50px color-mix(in srgb, var(--default-color), transparent 85%);
}

.call-to-action .image-wrapper img {
  width: 100%;
  height: 450px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.call-to-action .image-wrapper:hover img {
  transform: scale(1.05);
}

.call-to-action .image-wrapper .overlay-content {
  position: absolute;
  top: 30px;
  right: 30px;
  z-index: 2;
}

.call-to-action .image-wrapper .special-offer {
  background: linear-gradient(135deg, var(--accent-color), color-mix(in srgb, var(--accent-color), black 20%));
  padding: 15px 20px;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 8px 25px color-mix(in srgb, var(--accent-color), transparent 60%);
}

.call-to-action .image-wrapper .special-offer .label {
  display: block;
  color: var(--contrast-color);
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 5px;
  opacity: 0.9;
}

.call-to-action .image-wrapper .special-offer .discount {
  display: block;
  color: var(--contrast-color);
  font-size: 1.5rem;
  font-weight: 700;
}

@media (max-width: 768px) {
  .call-to-action .image-wrapper {
    margin-top: 2rem;
  }

  .call-to-action .image-wrapper .overlay-content {
    top: 20px;
    right: 20px;
  }

  .call-to-action .image-wrapper .special-offer {
    padding: 12px 16px;
  }

  .call-to-action .image-wrapper .special-offer .label {
    font-size: 0.8rem;
  }

  .call-to-action .image-wrapper .special-offer .discount {
    font-size: 1.3rem;
  }
}

@media (max-width: 992px) {
  .call-to-action .content {
    margin-bottom: 2rem;
  }
}

/*--------------------------------------------------------------
# Events Cards Section
--------------------------------------------------------------*/
.events-cards .event-item {
  background: var(--surface-color);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 40px color-mix(in srgb, var(--default-color), transparent 85%);
  transition: all 0.4s ease;
  height: 100%;
}

.events-cards .event-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px color-mix(in srgb, var(--default-color), transparent 75%);
}

.events-cards .event-item .event-header {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.events-cards .event-item .event-header .event-icon {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--accent-color), color-mix(in srgb, var(--accent-color), var(--heading-color) 30%));
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  box-shadow: 0 5px 20px color-mix(in srgb, var(--accent-color), transparent 60%);
}

.events-cards .event-item .event-header .event-icon i {
  font-size: 20px;
  color: var(--contrast-color);
}

.events-cards .event-item .event-header img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.events-cards .event-item .event-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 60%, color-mix(in srgb, var(--default-color), transparent 80%));
  z-index: 1;
}

.events-cards .event-item .event-content {
  padding: 25px;
}

.events-cards .event-item .event-content h4 {
  color: var(--heading-color);
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 15px;
  line-height: 1.3;
}

.events-cards .event-item .event-content p {
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  font-size: 15px;
  line-height: 1.6;
  margin-bottom: 20px;
}

.events-cards .event-item .event-content .event-features {
  margin-bottom: 25px;
}

.events-cards .event-item .event-content .event-features .feature-item {
  display: block;
  color: color-mix(in srgb, var(--default-color), transparent 10%);
  font-size: 14px;
  margin-bottom: 8px;
}

.events-cards .event-item .event-content .event-features .feature-item i {
  color: var(--accent-color);
  margin-right: 8px;
  font-size: 12px;
}

.events-cards .event-item .event-content .event-link {
  color: var(--accent-color);
  font-weight: 600;
  font-size: 16px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  transition: all 0.3s ease;
}

.events-cards .event-item .event-content .event-link i {
  margin-left: 8px;
  font-size: 14px;
  transition: transform 0.3s ease;
}

.events-cards .event-item .event-content .event-link:hover {
  color: var(--heading-color);
}

.events-cards .event-item .event-content .event-link:hover i {
  transform: translateX(5px);
}

.events-cards .event-item:hover .event-header img {
  transform: scale(1.05);
}

@media (max-width: 768px) {
  .events-cards .event-item .event-header {
    height: 180px;
  }

  .events-cards .event-item .event-header .event-icon {
    width: 45px;
    height: 45px;
    top: 15px;
    right: 15px;
  }

  .events-cards .event-item .event-header .event-icon i {
    font-size: 18px;
  }

  .events-cards .event-item .event-content {
    padding: 20px;
  }

  .events-cards .event-item .event-content h4 {
    font-size: 20px;
    margin-bottom: 12px;
  }

  .events-cards .event-item .event-content p {
    font-size: 14px;
    margin-bottom: 15px;
  }

  .events-cards .event-item .event-content .event-features {
    margin-bottom: 20px;
  }

  .events-cards .event-item .event-content .event-features .feature-item {
    font-size: 13px;
    margin-bottom: 6px;
  }

  .events-cards .event-item .event-content .event-link {
    font-size: 15px;
  }
}

@media (max-width: 992px) {
  .events-cards .row>div:nth-child(n+5) {
    margin-top: 2rem;
  }
}

/*--------------------------------------------------------------
# Location Cards Section
--------------------------------------------------------------*/
.location-cards .area-highlight {
  background: var(--surface-color);
  border-radius: 16px;
  overflow: hidden;
  height: 100%;
  transition: all 0.3s ease;
  border: 2px solid color-mix(in srgb, var(--default-color), transparent 94%);
}

.location-cards .area-highlight:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px color-mix(in srgb, var(--default-color), transparent 85%);
  border-color: color-mix(in srgb, var(--accent-color), transparent 80%);
}

.location-cards .area-highlight:hover .area-image-wrapper img {
  transform: scale(1.08);
}

.location-cards .area-highlight:hover .area-badge {
  background: var(--accent-color);
  color: var(--contrast-color);
}

.location-cards .area-highlight .area-image-wrapper {
  position: relative;
  height: 180px;
  overflow: hidden;
}

.location-cards .area-highlight .area-image-wrapper img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.location-cards .area-highlight .area-image-wrapper .area-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  background: color-mix(in srgb, var(--surface-color), transparent 10%);
  backdrop-filter: blur(10px);
  padding: 8px 12px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 600;
  color: var(--heading-color);
  transition: all 0.3s ease;
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 85%);
}

.location-cards .area-highlight .area-image-wrapper .area-badge i {
  font-size: 13px;
  color: var(--accent-color);
}

.location-cards .area-highlight .area-info {
  padding: 20px;
}

.location-cards .area-highlight .area-info h5 {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 8px;
  color: var(--heading-color);
  line-height: 1.3;
}

.location-cards .area-highlight .area-info p {
  font-size: 14px;
  color: color-mix(in srgb, var(--default-color), transparent 25%);
  line-height: 1.5;
  margin-bottom: 15px;
}

.location-cards .area-highlight .area-info .quick-stats {
  display: flex;
  gap: 15px;
}

.location-cards .area-highlight .area-info .quick-stats span {
  font-size: 12px;
  color: color-mix(in srgb, var(--default-color), transparent 35%);
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
}

.location-cards .area-highlight .area-info .quick-stats span i {
  color: var(--accent-color);
  font-size: 11px;
}

.location-cards .location-overview {
  background: linear-gradient(135deg, var(--accent-color), color-mix(in srgb, var(--accent-color), var(--heading-color) 30%));
  border-radius: 20px;
  padding: 40px 30px;
  color: var(--contrast-color);
  height: 100%;
  position: relative;
  overflow: hidden;
}

.location-cards .location-overview::before {
  content: "";
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: color-mix(in srgb, var(--contrast-color), transparent 95%);
  border-radius: 50%;
  z-index: 0;
}

.location-cards .location-overview .overview-header {
  position: relative;
  z-index: 2;
  text-align: center;
  margin-bottom: 30px;
}

.location-cards .location-overview .overview-header .location-marker {
  width: 70px;
  height: 70px;
  background: color-mix(in srgb, var(--contrast-color), transparent 85%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}

.location-cards .location-overview .overview-header .location-marker i {
  font-size: 30px;
  color: var(--accent-color);
}

.location-cards .location-overview .overview-header h3 {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 12px;
  color: var(--contrast-color);
}

.location-cards .location-overview .overview-header .overview-subtitle {
  font-size: 14px;
  opacity: 0.9;
  line-height: 1.5;
  margin: 0;
}

.location-cards .location-overview .benefits-list {
  position: relative;
  z-index: 2;
}

.location-cards .location-overview .benefits-list .benefit-item {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 18px;
  padding: 12px 15px;
  background: color-mix(in srgb, var(--contrast-color), transparent 90%);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.location-cards .location-overview .benefits-list .benefit-item:hover {
  background: color-mix(in srgb, var(--contrast-color), transparent 85%);
  transform: translateX(5px);
}

.location-cards .location-overview .benefits-list .benefit-item .benefit-icon {
  width: 40px;
  height: 40px;
  background: var(--contrast-color);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.location-cards .location-overview .benefits-list .benefit-item .benefit-icon i {
  font-size: 18px;
  color: var(--accent-color);
}

.location-cards .location-overview .benefits-list .benefit-item .benefit-content h6 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 2px;
  color: var(--contrast-color);
}

.location-cards .location-overview .benefits-list .benefit-item .benefit-content span {
  font-size: 12px;
  opacity: 0.8;
}

.location-cards .location-overview .location-guide-btn {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background: var(--contrast-color);
  color: var(--accent-color);
  padding: 14px 24px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  text-decoration: none;
  margin-top: 20px;
  transition: all 0.3s ease;
}

.location-cards .location-overview .location-guide-btn:hover {
  background: color-mix(in srgb, var(--contrast-color), transparent 10%);
  transform: translateY(-2px);
}

.location-cards .location-overview .location-guide-btn:hover i {
  transform: translateX(3px);
}

.location-cards .location-overview .location-guide-btn i {
  font-size: 14px;
  transition: transform 0.3s ease;
}

@media (max-width: 992px) {
  .location-cards .location-overview {
    margin-top: 30px;
  }

  .location-cards .location-overview .overview-header .location-marker {
    width: 60px;
    height: 60px;
  }

  .location-cards .location-overview .overview-header .location-marker i {
    font-size: 26px;
  }
}

@media (max-width: 768px) {
  .location-cards .area-highlight .area-image-wrapper {
    height: 160px;
  }

  .location-cards .area-highlight .area-image-wrapper .area-badge {
    padding: 6px 10px;
    font-size: 11px;
  }

  .location-cards .area-highlight .area-info {
    padding: 18px;
  }

  .location-cards .area-highlight .area-info .quick-stats {
    flex-direction: column;
    gap: 8px;
  }

  .location-cards .location-overview {
    padding: 30px 25px;
  }

  .location-cards .location-overview .overview-header h3 {
    font-size: 20px;
  }

  .location-cards .location-overview .benefits-list .benefit-item {
    padding: 10px 12px;
  }

  .location-cards .location-overview .benefits-list .benefit-item .benefit-icon {
    width: 35px;
    height: 35px;
  }

  .location-cards .location-overview .benefits-list .benefit-item .benefit-icon i {
    font-size: 16px;
  }
}

/*--------------------------------------------------------------
# Gallery Showcase Section
--------------------------------------------------------------*/
.gallery-showcase .gallery-carousel {
  margin-bottom: 2rem;
}

.gallery-showcase .gallery-carousel .swiper-wrapper {
  height: auto !important;
}

.gallery-showcase .gallery-item {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.gallery-showcase .gallery-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.gallery-showcase .gallery-item:hover .gallery-overlay {
  opacity: 1;
}

.gallery-showcase .gallery-item:hover img {
  transform: scale(1.05);
}

.gallery-showcase .gallery-item img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

@media (max-width: 576px) {
  .gallery-showcase .gallery-item img {
    height: 200px;
  }
}

.gallery-showcase .gallery-item .gallery-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--accent-color), color-mix(in srgb, var(--accent-color), transparent 60%));
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gallery-showcase .gallery-item .gallery-overlay i {
  color: var(--contrast-color);
  font-size: 2rem;
}

.gallery-showcase .btn-gallery {
  background: linear-gradient(135deg, var(--accent-color), color-mix(in srgb, var(--accent-color), #000000 20%));
  color: var(--contrast-color);
  border: none;
  padding: 15px 35px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 50px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.gallery-showcase .btn-gallery:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
  color: var(--contrast-color);
  background: linear-gradient(135deg, color-mix(in srgb, var(--accent-color), #000000 10%), color-mix(in srgb, var(--accent-color), #000000 30%));
}

.gallery-showcase .btn-gallery i {
  font-size: 1.2rem;
}

@media (max-width: 576px) {
  .gallery-showcase .btn-gallery {
    padding: 12px 28px;
    font-size: 1rem;
  }
}

/*--------------------------------------------------------------
# About Section
--------------------------------------------------------------*/
.about .image-stack {
  position: relative;
}

.about .image-stack .main-image-wrapper {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.about .image-stack .main-image-wrapper .main-image {
  width: 100%;
  height: 500px;
  object-fit: cover;
}

.about .image-stack .main-image-wrapper .floating-card {
  position: absolute;
  top: 30px;
  right: 30px;
  background: var(--surface-color);
  padding: 1.5rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.about .image-stack .main-image-wrapper .floating-card .card-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
}

.about .image-stack .main-image-wrapper .floating-card .card-icon i {
  color: #b8860b;
  font-size: 1.2rem;
}

.about .image-stack .main-image-wrapper .floating-card h6 {
  font-size: 1rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.about .image-stack .main-image-wrapper .floating-card p {
  font-size: 0.85rem;
  margin: 0;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
}

.about .image-stack .secondary-image {
  position: absolute;
  bottom: -50px;
  left: -30px;
  width: 200px;
  height: 150px;
  border-radius: 15px;
  overflow: hidden;
  border: 5px solid var(--surface-color);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.about .image-stack .secondary-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.about .content-wrapper {
  padding-left: 3rem;
}

@media (max-width: 992px) {
  .about .content-wrapper {
    padding-left: 0;
    margin-top: 4rem;
  }
}

.about .content-wrapper .badge {
  display: inline-block;
  background: color-mix(in srgb, var(--accent-color), transparent 85%);
  color: var(--accent-color);
  padding: 8px 20px;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  border: 1px solid color-mix(in srgb, var(--accent-color), transparent 70%);
}

.about .content-wrapper h2 {
  font-size: 2.8rem;
  line-height: 1.2;
  margin-bottom: 1.5rem;
}

@media (max-width: 768px) {
  .about .content-wrapper h2 {
    font-size: 2.2rem;
  }
}

.about .content-wrapper .lead {
  font-size: 1.2rem;
  font-weight: 500;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  color: color-mix(in srgb, var(--default-color), transparent 10%);
}

.about .content-wrapper p {
  font-size: 1rem;
  line-height: 1.7;
  margin-bottom: 2rem;
  color: color-mix(in srgb, var(--default-color), transparent 25%);
}

.about .milestone-timeline {
  margin: 3rem 0;
}

.about .milestone-item {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid color-mix(in srgb, var(--default-color), transparent 85%);
}

.about .milestone-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.about .milestone-item .milestone-year {
  width: 80px;
  height: 80px;
  background: var(--accent-color);
  color: var(--contrast-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1rem;
  margin-right: 2rem;
  flex-shrink: 0;
}

.about .milestone-item .milestone-content h5 {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: var(--heading-color);
}

.about .milestone-item .milestone-content p {
  font-size: 0.95rem;
  margin: 0;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
}

.about .action-buttons {
  display: flex;
  gap: 1.5rem;
  margin-top: 2rem;
}

@media (max-width: 576px) {
  .about .action-buttons {
    flex-direction: column;
    gap: 1rem;
  }
}

.about .action-buttons .btn-explore {
  background: var(--accent-color);
  color: var(--contrast-color);
  padding: 14px 30px;
  border-radius: 30px;
  text-decoration: none;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.about .action-buttons .btn-explore:hover {
  background: color-mix(in srgb, var(--accent-color), black 15%);
  transform: translateY(-2px);
  color: var(--contrast-color);
}

.about .action-buttons .btn-video {
  background: transparent;
  color: var(--accent-color);
  padding: 14px 30px;
  border: 2px solid var(--accent-color);
  border-radius: 30px;
  text-decoration: none;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.about .action-buttons .btn-video:hover {
  background: var(--accent-color);
  color: var(--contrast-color);
}

.about .features-showcase {
  margin-top: 6rem;
  margin-bottom: 3rem;
}

.about .features-showcase .features-header h3 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

@media (max-width: 768px) {
  .about .features-showcase .features-header h3 {
    font-size: 2rem;
  }
}

.about .features-showcase .features-header p {
  font-size: 1.1rem;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  margin-bottom: 0;
}

.about .feature-card {
  margin-bottom: 2rem;
}

.about .feature-card .feature-visual {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  height: 250px;
  margin-bottom: 1.5rem;
}

.about .feature-card .feature-visual img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.about .feature-card .feature-visual .feature-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6));
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.about .feature-card .feature-visual .feature-icon {
  width: 70px;
  height: 70px;
  background: var(--accent-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.about .feature-card .feature-visual .feature-icon i {
  font-size: 1.8rem;
  color: var(--contrast-color);
}

.about .feature-card .feature-visual:hover .feature-overlay {
  opacity: 1;
}

.about .feature-card .feature-visual:hover img {
  transform: scale(1.05);
}

.about .feature-card .feature-details h4 {
  font-size: 1.4rem;
  margin-bottom: 1rem;
  color: var(--heading-color);
}

.about .feature-card .feature-details p {
  font-size: 1rem;
  line-height: 1.6;
  color: color-mix(in srgb, var(--default-color), transparent 25%);
  margin: 0;
}

.about .achievements-section {
  margin-top: 5rem;
  padding: 4rem 0;
  background: linear-gradient(135deg, color-mix(in srgb, var(--accent-color), transparent 95%), color-mix(in srgb, var(--accent-color), transparent 90%));
  border-radius: 25px;
}

.about .achievements-section .achievements-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
}

@media (max-width: 768px) {
  .about .achievements-section .achievements-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .about .achievements-section .achievements-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

.about .achievements-section .achievement-stat {
  text-align: center;
}

.about .achievements-section .achievement-stat .stat-counter {
  font-size: 3.5rem;
  font-weight: 700;
  color: var(--accent-color);
  line-height: 1;
  margin-bottom: 0.5rem;
  font-family: var(--heading-font);
}

.about .achievements-section .achievement-stat .stat-description {
  font-size: 1rem;
  color: var(--heading-color);
  font-weight: 500;
}

@media (max-width: 992px) {
  .about .image-stack {
    margin-bottom: 2rem;
  }

  .about .image-stack .secondary-image {
    position: static;
    width: 100%;
    height: 200px;
    margin-top: 1rem;
  }
}

/*--------------------------------------------------------------
# Rooms 2 Section
--------------------------------------------------------------*/
.rooms-2 .room-filters {
  background-color: var(--surface-color);
  padding: 30px;
  border-radius: 10px;
  margin-bottom: 40px;
  box-shadow: 0 4px 20px color-mix(in srgb, var(--default-color), transparent 90%);
}

.rooms-2 .room-filters .form-label {
  font-weight: 600;
  color: var(--heading-color);
  margin-bottom: 8px;
  font-size: 14px;
}

.rooms-2 .room-filters .form-select {
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 80%);
  border-radius: 8px;
  padding: 10px 15px;
  font-size: 14px;
  color: var(--default-color);
  background-color: var(--background-color);
  transition: all 0.3s ease;
}

.rooms-2 .room-filters .form-select:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 0.2rem color-mix(in srgb, var(--accent-color), transparent 75%);
}

.rooms-2 .rooms-grid .room-card {
  background-color: var(--surface-color);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 8px 30px color-mix(in srgb, var(--default-color), transparent 88%);
  transition: all 0.3s ease;
  height: 100%;
}

.rooms-2 .rooms-grid .room-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px color-mix(in srgb, var(--default-color), transparent 80%);
}

.rooms-2 .rooms-grid .room-card .room-image {
  position: relative;
  overflow: hidden;
  height: 250px;
}

.rooms-2 .rooms-grid .room-card .room-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.rooms-2 .rooms-grid .room-card .room-image:hover img {
  transform: scale(1.05);
}

.rooms-2 .rooms-grid .room-card .room-image .room-features {
  position: absolute;
  top: 15px;
  left: 15px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.rooms-2 .rooms-grid .room-card .room-image .room-features .feature-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  color: var(--contrast-color);
}

.rooms-2 .rooms-grid .room-card .room-image .room-features .feature-badge.ocean {
  background-color: #007bff;
}

.rooms-2 .rooms-grid .room-card .room-image .room-features .feature-badge.popular {
  background-color: #ff6b35;
}

.rooms-2 .rooms-grid .room-card .room-image .room-features .feature-badge.business {
  background-color: #6c757d;
}

.rooms-2 .rooms-grid .room-card .room-image .room-features .feature-badge.family {
  background-color: #28a745;
}

.rooms-2 .rooms-grid .room-card .room-image .room-features .feature-badge.garden {
  background-color: #20c997;
}

.rooms-2 .rooms-grid .room-card .room-image .room-features .feature-badge.romantic {
  background-color: #e91e63;
}

.rooms-2 .rooms-grid .room-card .room-image .room-features .feature-badge.premium {
  background-color: #ffc107;
  color: var(--default-color);
}

.rooms-2 .rooms-grid .room-card .room-image .room-features .feature-badge.city {
  background-color: #17a2b8;
}

.rooms-2 .rooms-grid .room-card .room-image .room-features .feature-badge.luxury {
  background-color: #6f42c1;
}

.rooms-2 .rooms-grid .room-card .room-content {
  padding: 25px;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.rooms-2 .rooms-grid .room-card .room-content .room-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.rooms-2 .rooms-grid .room-card .room-content .room-header h3 {
  font-size: 20px;
  font-weight: 700;
  margin: 0;
  color: var(--heading-color);
}

.rooms-2 .rooms-grid .room-card .room-content .room-header .room-rating {
  display: flex;
  gap: 3px;
  font-size: 14px;
  color: #ffd700;
}

.rooms-2 .rooms-grid .room-card .room-content .room-description {
  color: var(--default-color);
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 20px;
  flex-grow: 1;
}

.rooms-2 .rooms-grid .room-card .room-content .room-amenities {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 25px;
}

.rooms-2 .rooms-grid .room-card .room-content .room-amenities span {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: var(--default-color);
}

.rooms-2 .rooms-grid .room-card .room-content .room-amenities span i {
  color: var(--accent-color);
}

.rooms-2 .rooms-grid .room-card .room-content .room-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.rooms-2 .rooms-grid .room-card .room-content .room-footer .room-price {
  display: flex;
  align-items: baseline;
  gap: 5px;
}

.rooms-2 .rooms-grid .room-card .room-content .room-footer .room-price .price-from {
  font-size: 12px;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
}

.rooms-2 .rooms-grid .room-card .room-content .room-footer .room-price .price-amount {
  font-size: 24px;
  font-weight: 700;
  color: var(--accent-color);
}

.rooms-2 .rooms-grid .room-card .room-content .room-footer .room-price .price-period {
  font-size: 14px;
  color: var(--default-color);
}

.rooms-2 .rooms-grid .room-card .room-content .room-footer .btn-room-details {
  background-color: var(--accent-color);
  color: var(--contrast-color);
  padding: 12px 24px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
}

.rooms-2 .rooms-grid .room-card .room-content .room-footer .btn-room-details:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 15%);
  transform: translateY(-2px);
}

.rooms-2 .load-more-section {
  margin-top: 50px;
}

.rooms-2 .load-more-section .btn-load-more {
  background-color: transparent;
  border: 2px solid var(--accent-color);
  color: var(--accent-color);
  padding: 15px 30px;
  border-radius: 50px;
  font-weight: 600;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.rooms-2 .load-more-section .btn-load-more i {
  font-size: 18px;
}

.rooms-2 .load-more-section .btn-load-more:hover {
  background-color: var(--accent-color);
  color: var(--contrast-color);
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .rooms-2 .room-filters {
    padding: 20px;
  }

  .rooms-2 .rooms-grid .room-card .room-content {
    padding: 20px;
  }

  .rooms-2 .rooms-grid .room-card .room-content .room-footer {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .rooms-2 .rooms-grid .room-card .room-content .room-footer .btn-room-details {
    width: 100%;
    text-align: center;
  }
}

/*--------------------------------------------------------------
# Room Details Section
--------------------------------------------------------------*/
.room-details .room-header-image {
  position: relative;
}

.room-details .room-header-image img {
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.room-details .room-header-image .room-badge {
  position: absolute;
  top: 20px;
  left: 20px;
  background: var(--accent-color);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
}

.room-details .room-header-content {
  padding-left: 30px;
}

@media (max-width: 992px) {
  .room-details .room-header-content {
    padding-left: 0;
    margin-top: 30px;
  }
}

.room-details .room-rating {
  display: flex;
  align-items: center;
  gap: 15px;
}

.room-details .room-rating .rating-score {
  background: var(--accent-color);
  color: var(--contrast-color);
  padding: 8px 12px;
  border-radius: 6px;
  font-weight: 600;
  font-size: 16px;
}

.room-details .room-rating .stars {
  color: #ffc107;
  font-size: 18px;
}

.room-details .room-rating .stars i {
  margin-right: 2px;
}

.room-details .room-rating .reviews-count {
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  font-size: 14px;
}

.room-details .room-title {
  font-size: 2.5rem;
  margin-bottom: 15px;
  font-weight: 700;
}

@media (max-width: 768px) {
  .room-details .room-title {
    font-size: 2rem;
  }
}

.room-details .room-tagline {
  font-size: 18px;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin-bottom: 25px;
}

.room-details .room-capacity {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.room-details .room-capacity .capacity-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.room-details .room-capacity .capacity-item i {
  color: var(--accent-color);
  font-size: 16px;
}

.room-details .room-price {
  margin-bottom: 25px;
}

.room-details .room-price .price-amount {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--heading-color);
}

.room-details .room-price .price-period {
  font-size: 16px;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  margin-left: 8px;
}

.room-details .btn-book-now {
  background: var(--accent-color);
  color: var(--contrast-color);
  padding: 12px 30px;
  border-radius: 8px;
  border: none;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s;
}

.room-details .btn-book-now:hover {
  background: color-mix(in srgb, var(--accent-color), black 10%);
  transform: translateY(-2px);
}

.room-details .section-subtitle {
  font-size: 1.8rem;
  margin-bottom: 20px;
  color: var(--heading-color);
  font-weight: 600;
}

.room-details .room-gallery .gallery-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 15px;
}

@media (max-width: 768px) {
  .room-details .room-gallery .gallery-grid {
    grid-template-columns: 1fr;
  }
}

.room-details .room-gallery .gallery-main img {
  border-radius: 12px;
  width: 100%;
  height: 400px;
  object-fit: cover;
}

.room-details .room-gallery .gallery-thumbnails {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.room-details .room-gallery .gallery-thumbnails img {
  border-radius: 8px;
  width: 100%;
  height: 185px;
  object-fit: cover;
  transition: transform 0.3s;
}

.room-details .room-gallery .gallery-thumbnails img:hover {
  transform: scale(1.05);
}

.room-details .room-description p {
  font-size: 16px;
  line-height: 1.7;
  margin-bottom: 20px;
}

.room-details .highlight-box {
  background: var(--surface-color);
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  border-left: 4px solid var(--accent-color);
}

.room-details .highlight-box .highlight-icon {
  width: 60px;
  height: 60px;
  background: var(--accent-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.room-details .highlight-box .highlight-icon i {
  font-size: 24px;
  color: var(--contrast-color);
}

.room-details .highlight-box h4 {
  margin-bottom: 15px;
  font-size: 1.2rem;
}

.room-details .highlight-box .quote-author {
  margin-top: 15px;
  font-style: italic;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  font-size: 14px;
}

.room-details .room-amenities .amenity-category h5 {
  color: var(--heading-color);
  font-size: 1.1rem;
  margin-bottom: 15px;
  font-weight: 600;
}

.room-details .room-amenities .amenity-category ul {
  list-style: none;
  padding: 0;
}

.room-details .room-amenities .amenity-category ul li {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 15px;
}

.room-details .room-amenities .amenity-category ul li i {
  color: var(--accent-color);
  margin-right: 10px;
  font-size: 16px;
}

.room-details .room-tabs .nav-tabs {
  border: none;
  margin-bottom: 30px;
}

.room-details .room-tabs .nav-tabs .nav-link {
  border: none;
  background: transparent;
  color: var(--default-color);
  padding: 15px 30px;
  font-weight: 500;
  border-radius: 8px;
  margin-right: 10px;
}

.room-details .room-tabs .nav-tabs .nav-link.active {
  background: var(--accent-color);
  color: var(--contrast-color);
}

.room-details .room-tabs .nav-tabs .nav-link:hover {
  background: color-mix(in srgb, var(--accent-color), transparent 80%);
}

.room-details .room-tabs .tab-content-wrapper {
  background: var(--surface-color);
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.room-details .room-tabs .tab-content-wrapper h6 {
  color: var(--heading-color);
  font-weight: 600;
  margin-bottom: 10px;
  font-size: 16px;
}

.room-details .room-tabs .tab-content-wrapper ul {
  list-style: none;
  padding: 0;
}

.room-details .room-tabs .tab-content-wrapper ul li {
  padding: 5px 0;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
}

.room-details .room-addons .addon-card {
  background: var(--surface-color);
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s;
}

.room-details .room-addons .addon-card:hover {
  transform: translateY(-5px);
}

.room-details .room-addons .addon-card .addon-icon {
  width: 70px;
  height: 70px;
  background: color-mix(in srgb, var(--accent-color), transparent 90%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}

.room-details .room-addons .addon-card .addon-icon i {
  font-size: 28px;
  color: var(--accent-color);
}

.room-details .room-addons .addon-card h5 {
  margin-bottom: 15px;
  font-size: 1.2rem;
}

.room-details .room-addons .addon-card p {
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin-bottom: 15px;
  font-size: 14px;
  line-height: 1.6;
}

.room-details .room-addons .addon-card .addon-price {
  font-size: 18px;
  font-weight: 600;
  color: var(--accent-color);
}

.room-details .booking-cta .booking-card {
  background: linear-gradient(135deg, var(--accent-color), color-mix(in srgb, var(--accent-color), black 20%));
  color: var(--contrast-color);
  padding: 40px;
  border-radius: 15px;
}

.room-details .booking-cta .booking-card h4 {
  color: var(--contrast-color);
  margin-bottom: 10px;
  font-size: 1.5rem;
}

.room-details .booking-cta .booking-card p {
  color: color-mix(in srgb, var(--contrast-color), transparent 20%);
  margin-bottom: 0;
}

.room-details .booking-cta .booking-card .price-display {
  margin-bottom: 20px;
  text-align: right;
}

@media (max-width: 992px) {
  .room-details .booking-cta .booking-card .price-display {
    text-align: center;
    margin-top: 20px;
  }
}

.room-details .booking-cta .booking-card .price-display .price {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--contrast-color);
}

.room-details .booking-cta .booking-card .price-display .period {
  font-size: 16px;
  color: color-mix(in srgb, var(--contrast-color), transparent 30%);
  display: block;
}

.room-details .booking-cta .booking-card .btn-primary {
  background: var(--accent-color);
  border: none;
  padding: 12px 30px;
  font-weight: 600;
  border-radius: 8px;
}

.room-details .booking-cta .booking-card .btn-primary:hover {
  background: color-mix(in srgb, var(--accent-color), black 20%);
}

/*--------------------------------------------------------------
# Amenities Section
--------------------------------------------------------------*/
.amenities .amenity-card {
  background: var(--surface-color);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 25px color-mix(in srgb, var(--default-color), transparent 90%);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
}

.amenities .amenity-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px color-mix(in srgb, var(--default-color), transparent 85%);
}

.amenities .amenity-card .amenity-image {
  position: relative;
  overflow: hidden;
  height: 250px;
}

.amenities .amenity-card .amenity-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.amenities .amenity-card .amenity-image::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, color-mix(in srgb, var(--accent-color), transparent 85%) 0%, color-mix(in srgb, var(--heading-color), transparent 85%) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.amenities .amenity-card:hover .amenity-image img {
  transform: scale(1.05);
}

.amenities .amenity-card:hover .amenity-image::after {
  opacity: 1;
}

.amenities .amenity-card .amenity-content {
  padding: 30px;
}

.amenities .amenity-card .amenity-content h3 {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 15px;
  color: var(--heading-color);
}

.amenities .amenity-card .amenity-content p {
  color: var(--default-color);
  line-height: 1.6;
  margin-bottom: 20px;
}

.amenities .amenity-card .amenity-content .amenity-features {
  list-style: none;
  padding: 0;
  margin: 0;
}

.amenities .amenity-card .amenity-content .amenity-features li {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  color: var(--default-color);
}

.amenities .amenity-card .amenity-content .amenity-features li i {
  color: var(--accent-color);
  font-size: 16px;
  margin-right: 10px;
  width: 20px;
  text-align: center;
}

.amenities .amenity-card .amenity-content .amenity-features li:last-child {
  margin-bottom: 0;
}

.amenities .amenities-grid {
  background: var(--surface-color);
  border-radius: 16px;
  padding: 50px 40px;
  box-shadow: 0 10px 30px color-mix(in srgb, var(--default-color), transparent 90%);
}

.amenities .amenities-grid h3 {
  font-size: 32px;
  font-weight: 700;
  color: var(--heading-color);
}

.amenities .amenities-grid .service-item {
  text-align: center;
  padding: 20px;
  transition: transform 0.3s ease;
}

.amenities .amenities-grid .service-item:hover {
  transform: translateY(-3px);
}

.amenities .amenities-grid .service-item .service-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  background: linear-gradient(135deg, var(--accent-color), color-mix(in srgb, var(--accent-color), var(--heading-color) 30%));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
}

.amenities .amenities-grid .service-item .service-icon::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--accent-color), color-mix(in srgb, var(--accent-color), var(--heading-color) 30%));
  opacity: 0;
  transform: scale(1.2);
  transition: all 0.3s ease;
}

.amenities .amenities-grid .service-item .service-icon i {
  font-size: 32px;
  color: var(--contrast-color);
  position: relative;
  z-index: 2;
}

.amenities .amenities-grid .service-item:hover .service-icon::before {
  opacity: 1;
  transform: scale(1);
}

.amenities .amenities-grid .service-item h4 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--heading-color);
}

.amenities .amenities-grid .service-item p {
  color: var(--default-color);
  line-height: 1.5;
  margin: 0;
  font-size: 14px;
}

@media (max-width: 768px) {
  .amenities .amenity-card .amenity-content {
    padding: 20px;
  }

  .amenities .amenity-card .amenity-content h3 {
    font-size: 20px;
  }

  .amenities .amenities-grid {
    padding: 30px 20px;
  }

  .amenities .amenities-grid h3 {
    font-size: 28px;
  }

  .amenities .amenities-grid .service-item {
    padding: 15px;
  }

  .amenities .amenities-grid .service-item .service-icon {
    width: 60px;
    height: 60px;
  }

  .amenities .amenities-grid .service-item .service-icon i {
    font-size: 24px;
  }

  .amenities .amenities-grid .service-item h4 {
    font-size: 16px;
  }

  .amenities .amenities-grid .service-item p {
    font-size: 13px;
  }
}

/*--------------------------------------------------------------
# Offers Section
--------------------------------------------------------------*/
.offers .offer-card {
  background: var(--surface-color);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
}

.offers .offer-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.offers .offer-card .offer-image {
  position: relative;
  overflow: hidden;
}

.offers .offer-card .offer-image img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.offers .offer-card .offer-image:hover img {
  transform: scale(1.05);
}

.offers .offer-card .offer-image .offer-badge {
  position: absolute;
  top: 20px;
  right: 20px;
}

.offers .offer-card .offer-image .offer-badge .discount {
  background: var(--accent-color);
  color: var(--contrast-color);
  padding: 8px 16px;
  border-radius: 25px;
  font-weight: 600;
  font-size: 14px;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(54, 144, 231, 0.3);
}

.offers .offer-card .offer-content {
  padding: 30px;
}

.offers .offer-card .offer-content h3 {
  font-size: 24px;
  margin-bottom: 15px;
  color: var(--heading-color);
}

.offers .offer-card .offer-content h4 {
  font-size: 20px;
  margin-bottom: 12px;
  color: var(--heading-color);
}

.offers .offer-card .offer-content p {
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  line-height: 1.6;
  margin-bottom: 20px;
}

.offers .offer-card .offer-content .offer-details {
  margin-bottom: 25px;
}

.offers .offer-card .offer-content .offer-details .price-info {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.offers .offer-card .offer-content .offer-details .price-info .original-price {
  text-decoration: line-through;
  color: color-mix(in srgb, var(--default-color), transparent 50%);
  font-size: 16px;
}

.offers .offer-card .offer-content .offer-details .price-info .discounted-price {
  font-size: 28px;
  font-weight: 700;
  color: var(--accent-color);
}

.offers .offer-card .offer-content .offer-details .price-info .per-night {
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  font-size: 14px;
}

.offers .offer-card .offer-content .offer-details .validity {
  display: flex;
  align-items: center;
  gap: 8px;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  font-size: 14px;
}

.offers .offer-card .offer-content .offer-details .validity i {
  color: var(--accent-color);
}

.offers .offer-card .offer-content .btn {
  padding: 12px 30px;
  border-radius: 25px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.offers .offer-card .offer-content .btn.btn-primary {
  background: var(--accent-color);
  border-color: var(--accent-color);
  color: var(--contrast-color);
}

.offers .offer-card .offer-content .btn.btn-primary:hover {
  background: color-mix(in srgb, var(--accent-color), black 10%);
  border-color: color-mix(in srgb, var(--accent-color), black 10%);
  transform: translateY(-1px);
}

.offers .offer-card .offer-content .btn.btn-outline-primary {
  border-color: var(--accent-color);
  color: var(--accent-color);
}

.offers .offer-card .offer-content .btn.btn-outline-primary:hover {
  background: var(--accent-color);
  border-color: var(--accent-color);
  color: var(--contrast-color);
  transform: translateY(-1px);
}

.offers .offer-card.compact .offer-image img {
  height: 180px;
}

.offers .offer-card.compact .offer-content {
  padding: 25px;
}

.offers .offer-card.compact .offer-content h4 {
  font-size: 18px;
  margin-bottom: 10px;
}

.offers .offer-card.compact .offer-content p {
  font-size: 14px;
  margin-bottom: 15px;
}

.offers .offer-card.compact .offer-content .offer-details {
  margin-bottom: 20px;
}

.offers .offer-card.compact .offer-content .btn {
  padding: 10px 25px;
  font-size: 14px;
}

@media (max-width: 768px) {
  .offers .offer-card .offer-content {
    padding: 25px 20px;
  }

  .offers .offer-card .offer-content h3 {
    font-size: 22px;
  }

  .offers .offer-card .offer-content .offer-details .price-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .offers .offer-card .offer-content .offer-details .price-info .discounted-price {
    font-size: 24px;
  }

  .offers .offer-card.compact .offer-content {
    padding: 20px 15px;
  }
}

/*--------------------------------------------------------------
# Restaurant Section
--------------------------------------------------------------*/
.restaurant .about-content h2 {
  margin-bottom: 1.5rem;
  font-weight: 700;
  position: relative;
}

.restaurant .about-content .fst-italic {
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
  color: color-mix(in srgb, var(--heading-color), transparent 20%);
}

.restaurant .about-content .feature-item {
  margin-bottom: 1.5rem;
  padding: 20px;
  background-color: var(--surface-color);
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.restaurant .about-content .feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.restaurant .about-content .feature-item i {
  font-size: 1.8rem;
  color: var(--accent-color);
  margin-bottom: 12px;
  display: inline-block;
}

.restaurant .about-content .feature-item h4 {
  font-size: 1.2rem;
  margin-bottom: 10px;
  font-weight: 600;
}

.restaurant .about-content .feature-item p {
  margin-bottom: 0;
  font-size: 0.95rem;
}

.restaurant .about-content .signature .chef-name {
  font-weight: 600;
  font-style: italic;
  margin-bottom: 5px;
}

.restaurant .about-content .signature img {
  max-width: 180px;
  height: auto;
  margin-top: 5px;
}

.restaurant .about-gallery {
  position: relative;
}

.restaurant .about-gallery .years-badge {
  position: absolute;
  bottom: 30px;
  right: 30px;
  z-index: 2;
  background-color: var(--accent-color);
  color: var(--contrast-color);
  padding: 15px 25px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.restaurant .about-gallery .years-badge .number {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1;
}

.restaurant .about-gallery .years-badge .text {
  font-size: 0.9rem;
  font-weight: 500;
}

.restaurant .about-gallery img {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.restaurant .about-gallery img:hover {
  transform: scale(1.02);
}

@media (max-width: 992px) {
  .restaurant .about-gallery {
    margin-top: 2rem;
  }

  .restaurant .about-gallery .years-badge {
    bottom: 20px;
    right: 20px;
    padding: 10px 20px;
  }

  .restaurant .about-gallery .years-badge .number {
    font-size: 2rem;
  }

  .restaurant .about-gallery .years-badge .text {
    font-size: 0.8rem;
  }
}

@media (max-width: 576px) {
  .restaurant .about-gallery .years-badge {
    bottom: 15px;
    right: 15px;
  }

  .restaurant .about-content .signature img {
    max-width: 150px;
  }
}

/*--------------------------------------------------------------
# Menu Section
--------------------------------------------------------------*/
.menu .menu-filters ul {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 15px;
  list-style: none;
  margin: 0;
  padding: 0;
}

.menu .menu-filters ul li {
  padding: 8px 16px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  border-radius: 25px;
  background-color: var(--surface-color);
  color: var(--default-color);
  transition: 0.3s;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.menu .menu-filters ul li:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 80%);
}

.menu .menu-filters ul li.filter-active {
  background-color: var(--accent-color);
  color: var(--contrast-color);
}

@media (max-width: 768px) {
  .menu .menu-filters ul {
    gap: 8px;
  }

  .menu .menu-filters ul li {
    padding: 6px 12px;
    font-size: 0.9rem;
  }
}

.menu .menu-container {
  position: relative;
}

.menu .specials-badge {
  margin-bottom: 15px;
}

.menu .specials-badge span {
  display: inline-block;
  background-color: color-mix(in srgb, var(--accent-color), transparent 90%);
  color: var(--heading-color);
  font-weight: 600;
  font-size: 1.1rem;
  padding: 8px 20px;
  border-radius: 20px;
}

.menu .specials-badge span i {
  color: var(--accent-color);
  margin-right: 8px;
}

.menu .specials-container {
  margin-bottom: 2rem;
}

.menu .special-item {
  overflow: hidden;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s, box-shadow 0.3s;
  height: 100%;
  background-color: var(--surface-color);
}

.menu .special-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.menu .special-item .menu-item-img {
  position: relative;
  overflow: hidden;
}

.menu .special-item .menu-item-img img {
  width: 100%;
  height: 240px;
  object-fit: cover;
  transition: transform 0.5s;
}

.menu .special-item .menu-item-img:hover img {
  transform: scale(1.05);
}

.menu .special-item .menu-item-img .menu-item-badges {
  position: absolute;
  top: 10px;
  left: 10px;
  display: flex;
  gap: 8px;
}

.menu .special-item .menu-item-img .menu-item-badges span {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 20px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.menu .special-item .menu-item-img .menu-item-badges .badge-special {
  background-color: var(--accent-color);
  color: var(--contrast-color);
}

.menu .special-item .menu-item-img .menu-item-badges .badge-vegan {
  background-color: color-mix(in srgb, green, transparent 30%);
  color: var(--contrast-color);
}

.menu .special-item .menu-item-img .menu-item-badges .badge-spicy {
  background-color: color-mix(in srgb, red, transparent 30%);
  color: var(--contrast-color);
}

.menu .special-item .menu-item-content {
  padding: 20px;
}

.menu .special-item .menu-item-content h4 {
  margin: 0 0 12px;
  font-weight: 600;
  font-size: 1.25rem;
}

.menu .special-item .menu-item-content p {
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin-bottom: 15px;
  font-size: 0.95rem;
}

.menu .special-item .menu-item-content .menu-item-price {
  font-weight: 700;
  color: var(--accent-color);
  font-size: 1.2rem;
}

.menu .menu-item {
  padding: 20px;
  border-radius: 8px;
  background-color: var(--surface-color);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.menu .menu-item:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-3px);
}

.menu .menu-item .menu-img {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 8px;
  transition: transform 0.3s;
}

.menu .menu-item .menu-img:hover {
  transform: scale(1.05);
}

.menu .menu-item .menu-content {
  flex: 1;
}

.menu .menu-item .menu-content h5 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.menu .menu-item .menu-content .menu-tag {
  font-size: 0.7rem;
  padding: 3px 8px;
  background-color: color-mix(in srgb, var(--accent-color), transparent 85%);
  color: var(--accent-color);
  border-radius: 20px;
  font-weight: 500;
  display: inline-block;
}

.menu .menu-item .menu-content p {
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  font-size: 0.9rem;
  margin-bottom: 10px;
}

.menu .menu-item .menu-content .price {
  font-weight: 700;
  color: var(--accent-color);
  font-size: 1.1rem;
}

.menu .download-menu {
  display: inline-block;
  padding: 10px 25px;
  background-color: var(--accent-color);
  color: var(--contrast-color);
  text-decoration: none;
  border-radius: 30px;
  font-weight: 600;
  transition: 0.3s;
}

.menu .download-menu i {
  margin-right: 8px;
}

.menu .download-menu:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 25%);
  transform: translateY(-3px);
}

@media (max-width: 992px) {
  .menu .menu-item .menu-img {
    width: 80px;
    height: 80px;
  }
}

@media (max-width: 768px) {
  .menu .specials-container .row {
    flex-direction: column;
  }
}

@media (max-width: 576px) {
  .menu .menu-item {
    flex-direction: column;
  }

  .menu .menu-item .menu-img {
    width: 100%;
    height: 160px;
    margin-bottom: 15px;
  }
}

/*--------------------------------------------------------------
# Booking Section
--------------------------------------------------------------*/
.booking {
  background: color-mix(in srgb, var(--surface-color), var(--background-color) 50%);
}

.booking .reservation-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.booking .reservation-header {
  margin-bottom: 4rem;
}

.booking .reservation-header h2 {
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

@media (max-width: 768px) {
  .booking .reservation-header h2 {
    font-size: 2rem;
  }
}

.booking .reservation-header .lead {
  font-size: 1.1rem;
  color: color-mix(in srgb, var(--default-color), transparent 25%);
  max-width: 600px;
  margin: 0 auto;
}

.booking .booking-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

@media (max-width: 992px) {
  .booking .booking-grid {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
}

.booking .booking-form-section .form-container {
  background: var(--surface-color);
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
  border-radius: 6px;
  padding: 2.5rem;
}

@media (max-width: 768px) {
  .booking .booking-form-section .form-container {
    padding: 2rem;
  }
}

.booking .reservation-form .form-section {
  margin-bottom: 2.5rem;
}

.booking .reservation-form .form-section h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
  color: var(--heading-color);
}

.booking .reservation-form .form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

@media (max-width: 576px) {
  .booking .reservation-form .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

.booking .reservation-form .form-grid .full-width {
  grid-column: 1/-1;
}

.booking .reservation-form .form-group {
  margin-bottom: 1.5rem;
}

.booking .reservation-form .form-label {
  font-weight: 500;
  font-size: 0.9rem;
  color: var(--heading-color);
  margin-bottom: 0.5rem;
  display: block;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.booking .reservation-form input[type=date],
.booking .reservation-form input[type=text],
.booking .reservation-form input[type=email],
.booking .reservation-form input[type=tel],
.booking .reservation-form textarea,
.booking .reservation-form select {
  color: var(--default-color);
  background-color: var(--surface-color);
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 80%);
  border-radius: 4px;
  padding: 0.875rem 1rem;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  width: 100%;
}

.booking .reservation-form input[type=date]:focus,
.booking .reservation-form input[type=text]:focus,
.booking .reservation-form input[type=email]:focus,
.booking .reservation-form input[type=tel]:focus,
.booking .reservation-form textarea:focus,
.booking .reservation-form select:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 0.125rem color-mix(in srgb, var(--accent-color), transparent 85%);
  outline: none;
}

.booking .reservation-form input[type=date]::placeholder,
.booking .reservation-form input[type=text]::placeholder,
.booking .reservation-form input[type=email]::placeholder,
.booking .reservation-form input[type=tel]::placeholder,
.booking .reservation-form textarea::placeholder,
.booking .reservation-form select::placeholder {
  color: color-mix(in srgb, var(--default-color), transparent 70%);
  font-size: 0.9rem;
}

.booking .reservation-form textarea {
  resize: vertical;
  min-height: 100px;
}

.booking .reservation-form .form-actions {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
}

.booking .reservation-form .btn-primary {
  background: var(--accent-color);
  border: 1px solid var(--accent-color);
  color: var(--contrast-color);
  border-radius: 4px;
  padding: 1rem 2.5rem;
  font-size: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  width: 100%;
}

.booking .reservation-form .btn-primary:hover {
  background: color-mix(in srgb, var(--accent-color), #000000 10%);
  border-color: color-mix(in srgb, var(--accent-color), #000000 10%);
  transform: translateY(-1px);
}

.booking .reservation-form .btn-primary:focus {
  box-shadow: 0 0 0 0.125rem color-mix(in srgb, var(--accent-color), transparent 75%);
}

.booking .hotel-showcase .showcase-image {
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 2rem;
}

.booking .hotel-showcase .showcase-image img {
  width: 100%;
  height: 280px;
  object-fit: cover;
}

.booking .hotel-showcase .hotel-highlights {
  background: var(--surface-color);
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
  border-radius: 6px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.booking .hotel-showcase .hotel-highlights h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  text-align: center;
}

.booking .hotel-showcase .hotel-highlights .highlights-grid {
  display: grid;
  gap: 1.5rem;
}

.booking .hotel-showcase .hotel-highlights .highlight-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.booking .hotel-showcase .hotel-highlights .highlight-item .highlight-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  background: color-mix(in srgb, var(--accent-color), transparent 90%);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.booking .hotel-showcase .hotel-highlights .highlight-item .highlight-icon i {
  font-size: 1.1rem;
  color: var(--accent-color);
}

.booking .hotel-showcase .hotel-highlights .highlight-item .highlight-content h5 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--heading-color);
}

.booking .hotel-showcase .hotel-highlights .highlight-item .highlight-content p {
  font-size: 0.9rem;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  margin: 0;
  line-height: 1.4;
}

.booking .hotel-showcase .booking-guarantees {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
}

@media (max-width: 768px) {
  .booking .hotel-showcase .booking-guarantees {
    flex-direction: column;
    gap: 0.75rem;
  }
}

.booking .hotel-showcase .booking-guarantees .guarantee-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--heading-color);
}

.booking .hotel-showcase .booking-guarantees .guarantee-item i {
  font-size: 1.1rem;
  color: var(--accent-color);
}

@media (max-width: 992px) {
  .booking .hotel-showcase {
    order: -1;
  }

  .booking .hotel-showcase .showcase-image img {
    height: 240px;
  }

  .booking .hotel-showcase .hotel-highlights .highlights-grid {
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  .booking .hotel-showcase .hotel-highlights .highlight-item {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .booking .hotel-showcase .hotel-highlights .highlight-item .highlight-content h5 {
    font-size: 0.95rem;
  }

  .booking .hotel-showcase .hotel-highlights .highlight-item .highlight-content p {
    font-size: 0.85rem;
  }

  .booking .hotel-showcase .booking-guarantees {
    justify-content: center;
  }

  .booking .hotel-showcase .booking-guarantees .guarantee-item {
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .booking .hotel-highlights .highlights-grid {
    grid-template-columns: 1fr;
  }
}

/*--------------------------------------------------------------
# Gallery Section
--------------------------------------------------------------*/
.gallery .hotel-filters {
  list-style: none;
  padding: 0;
  margin: 0 auto 40px auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10px;
}

.gallery .hotel-filters li {
  background: transparent;
  border: 2px solid var(--accent-color);
  color: var(--accent-color);
  padding: 10px 24px;
  border-radius: 30px;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.gallery .hotel-filters li:hover,
.gallery .hotel-filters li.filter-active {
  background: var(--accent-color);
  color: var(--contrast-color);
}

@media (max-width: 768px) {
  .gallery .hotel-filters li {
    padding: 8px 16px;
    font-size: 13px;
  }
}

.gallery .gallery-item {
  margin-bottom: 30px;
}

.gallery .gallery-item .gallery-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  cursor: pointer;
}

.gallery .gallery-item .gallery-wrapper:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.gallery .gallery-item .gallery-wrapper img {
  width: 100%;
  height: 280px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

@media (max-width: 576px) {
  .gallery .gallery-item .gallery-wrapper img {
    height: 250px;
  }
}

.gallery .gallery-item .gallery-wrapper:hover img {
  transform: scale(1.1);
}

.gallery .gallery-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gallery .gallery-overlay a {
  color: var(--contrast-color);
  font-size: 24px;
  width: 50px;
  height: 50px;
  background: var(--accent-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  transition: all 0.3s ease;
}

.gallery .gallery-overlay a:hover {
  background: color-mix(in srgb, var(--accent-color), transparent 20%);
  transform: scale(1.1);
}

.gallery .gallery-wrapper:hover .gallery-overlay {
  opacity: 1;
}

/*--------------------------------------------------------------
# Events Section
--------------------------------------------------------------*/
.events .hero-content {
  margin-bottom: 60px;
}

.events .hero-content h2 {
  font-size: 2.8rem;
  font-weight: 700;
  margin-bottom: 25px;
  background: linear-gradient(135deg, var(--heading-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@media (max-width: 768px) {
  .events .hero-content h2 {
    font-size: 2.2rem;
  }
}

.events .hero-content .lead {
  font-size: 1.2rem;
  max-width: 800px;
  margin: 0 auto;
  color: color-mix(in srgb, var(--default-color), transparent 15%);
}

.events .event-venues-grid {
  margin-bottom: 80px;
}

.events .event-venues-grid .venue-card {
  height: 320px;
  border-radius: 20px;
  overflow: hidden;
  position: relative;
  transition: all 0.4s ease;
}

.events .event-venues-grid .venue-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.events .event-venues-grid .venue-card:hover .venue-image img {
  transform: scale(1.1);
}

.events .event-venues-grid .venue-card:hover .venue-overlay {
  opacity: 1;
}

.events .event-venues-grid .venue-card .venue-image {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.events .event-venues-grid .venue-card .venue-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.events .event-venues-grid .venue-card .venue-image .venue-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.3));
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.events .event-venues-grid .venue-card .venue-image .venue-overlay .venue-info {
  text-align: center;
  color: var(--contrast-color);
}

.events .event-venues-grid .venue-card .venue-image .venue-overlay .venue-info h4 {
  font-size: 1.6rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--contrast-color);
}

.events .event-venues-grid .venue-card .venue-image .venue-overlay .venue-info .capacity {
  font-size: 1rem;
  opacity: 0.9;
}

.events .features-section {
  margin-bottom: 80px;
}

.events .features-section .features-content h3 {
  font-size: 2.4rem;
  font-weight: 600;
  margin-bottom: 20px;
  color: var(--heading-color);
}

.events .features-section .features-content p {
  font-size: 1.1rem;
  line-height: 1.7;
  margin-bottom: 40px;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
}

.events .features-section .feature-list .feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 35px;
}

.events .features-section .feature-list .feature-item .feature-icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--accent-color), color-mix(in srgb, var(--accent-color), black 20%));
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 25px;
  flex-shrink: 0;
}

.events .features-section .feature-list .feature-item .feature-icon i {
  font-size: 1.7rem;
  color: var(--contrast-color);
}

.events .features-section .feature-list .feature-item .feature-details h5 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--heading-color);
}

.events .features-section .feature-list .feature-item .feature-details p {
  margin: 0;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  line-height: 1.5;
}

.events .features-section .features-gallery .gallery-main {
  border-radius: 20px;
  overflow: hidden;
  margin-bottom: 20px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.events .features-section .features-gallery .gallery-main img {
  width: 100%;
  height: 350px;
  object-fit: cover;
}

.events .features-section .features-gallery .gallery-thumbnails {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.events .features-section .features-gallery .gallery-thumbnails .thumbnail-item {
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}

.events .features-section .features-gallery .gallery-thumbnails .thumbnail-item img {
  width: 100%;
  height: 140px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.events .features-section .features-gallery .gallery-thumbnails .thumbnail-item:hover img {
  transform: scale(1.05);
}

@media (max-width: 992px) {
  .events .features-section .row {
    flex-direction: column-reverse;
  }

  .events .features-section .features-gallery {
    margin-bottom: 40px;
  }
}

.events .event-packages {
  margin-bottom: 80px;
}

.events .event-packages .section-header {
  margin-bottom: 60px;
}

.events .event-packages .section-header h3 {
  font-size: 2.4rem;
  font-weight: 600;
  margin-bottom: 20px;
  color: var(--heading-color);
}

.events .event-packages .section-header p {
  font-size: 1.1rem;
  max-width: 700px;
  margin: 0 auto;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
}

.events .event-packages .package-item {
  background: var(--surface-color);
  border-radius: 20px;
  padding: 40px 30px;
  height: 100%;
  position: relative;
  border: 2px solid color-mix(in srgb, var(--default-color), transparent 90%);
  transition: all 0.4s ease;
}

.events .event-packages .package-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
  border-color: var(--accent-color);
}

.events .event-packages .package-item.featured {
  border-color: var(--accent-color);
  background: linear-gradient(135deg, var(--surface-color), color-mix(in srgb, var(--accent-color), transparent 95%));
}

.events .event-packages .package-item.featured .featured-label {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--accent-color);
  color: var(--contrast-color);
  padding: 6px 20px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 600;
}

.events .event-packages .package-item .package-content {
  text-align: center;
}

.events .event-packages .package-item .package-content .package-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: color-mix(in srgb, var(--accent-color), transparent 90%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 25px;
}

.events .event-packages .package-item .package-content .package-icon i {
  font-size: 2rem;
  color: var(--accent-color);
}

.events .event-packages .package-item .package-content h4 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--heading-color);
}

.events .event-packages .package-item .package-content p {
  line-height: 1.6;
  margin-bottom: 25px;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
}

.events .event-packages .package-item .package-content .package-highlights {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
  margin-bottom: 25px;
}

.events .event-packages .package-item .package-content .package-highlights .highlight {
  background: color-mix(in srgb, var(--accent-color), transparent 85%);
  color: var(--accent-color);
  font-size: 0.85rem;
  padding: 6px 14px;
  border-radius: 20px;
  font-weight: 500;
}

.events .event-packages .package-item .package-content .package-price {
  font-size: 1.1rem;
  margin-bottom: 25px;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
}

.events .event-packages .package-item .package-content .package-price strong {
  color: var(--accent-color);
  font-size: 1.3rem;
  font-weight: 700;
}

.events .event-packages .package-item .package-content .btn-package {
  background: var(--accent-color);
  color: var(--contrast-color);
  padding: 14px 30px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  display: inline-block;
}

.events .event-packages .package-item .package-content .btn-package:hover {
  background: color-mix(in srgb, var(--accent-color), black 15%);
  color: var(--contrast-color);
  transform: translateY(-2px);
}

.events .planning-services .planning-content h3 {
  font-size: 2.2rem;
  font-weight: 600;
  margin-bottom: 20px;
  color: var(--heading-color);
}

.events .planning-services .planning-content p {
  font-size: 1.1rem;
  line-height: 1.7;
  margin-bottom: 30px;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
}

.events .planning-services .planning-content .btn-contact {
  background: var(--accent-color);
  color: var(--contrast-color);
  padding: 16px 32px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  display: inline-block;
}

.events .planning-services .planning-content .btn-contact:hover {
  background: color-mix(in srgb, var(--accent-color), black 15%);
  color: var(--contrast-color);
  transform: translateY(-2px);
}

.events .planning-services .services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 25px;
}

.events .planning-services .services-grid .service-box {
  background: var(--surface-color);
  padding: 30px 25px;
  border-radius: 15px;
  text-align: center;
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
  transition: all 0.3s ease;
}

.events .planning-services .services-grid .service-box:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  border-color: var(--accent-color);
}

.events .planning-services .services-grid .service-box .service-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: color-mix(in srgb, var(--accent-color), transparent 90%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}

.events .planning-services .services-grid .service-box .service-icon i {
  font-size: 1.5rem;
  color: var(--accent-color);
}

.events .planning-services .services-grid .service-box h5 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--heading-color);
}

.events .planning-services .services-grid .service-box p {
  margin: 0;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  line-height: 1.5;
  font-size: 0.95rem;
}

@media (max-width: 768px) {
  .events .hero-content {
    margin-bottom: 40px;
  }

  .events .hero-content .lead {
    font-size: 1rem;
  }

  .events .event-venues-grid,
  .events .features-section,
  .events .event-packages,
  .events .planning-services {
    margin-bottom: 60px;
  }

  .events .venue-card {
    height: 250px;
  }

  .events .features-gallery .gallery-main img {
    height: 250px;
  }

  .events .features-gallery .gallery-thumbnails .thumbnail-item img {
    height: 100px;
  }

  .events .package-item {
    padding: 30px 20px;
  }

  .events .services-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .events .services-grid .service-box {
    padding: 25px 20px;
  }
}

/*--------------------------------------------------------------
# Hotel Location Section
--------------------------------------------------------------*/
.hotel-location .location-content {
  padding: 40px 0;
}

.hotel-location .location-content .location-badge {
  background: color-mix(in srgb, var(--accent-color), transparent 90%);
  color: var(--accent-color);
  display: inline-flex;
  align-items: center;
  padding: 8px 20px;
  border-radius: 50px;
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 20px;
}

.hotel-location .location-content .location-badge i {
  margin-right: 8px;
  font-size: 16px;
}

.hotel-location .location-content .location-title {
  font-size: 42px;
  color: var(--heading-color);
  margin-bottom: 20px;
  line-height: 1.2;
}

.hotel-location .location-content .location-description {
  font-size: 16px;
  color: color-mix(in srgb, var(--default-color), transparent 25%);
  margin-bottom: 40px;
  line-height: 1.7;
}

.hotel-location .location-content .info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
  margin-bottom: 40px;
}

.hotel-location .location-content .info-grid .info-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.hotel-location .location-content .info-grid .info-item .info-icon {
  background: var(--surface-color);
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px color-mix(in srgb, var(--accent-color), transparent 85%);
  flex-shrink: 0;
}

.hotel-location .location-content .info-grid .info-item .info-icon i {
  color: var(--accent-color);
  font-size: 20px;
}

.hotel-location .location-content .info-grid .info-item .info-text h6 {
  color: var(--heading-color);
  font-weight: 600;
  font-size: 15px;
  margin-bottom: 8px;
}

.hotel-location .location-content .info-grid .info-item .info-text p {
  color: color-mix(in srgb, var(--default-color), transparent 35%);
  line-height: 1.5;
  margin: 0;
  font-size: 14px;
}

.hotel-location .location-content .action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.hotel-location .location-content .action-buttons .btn {
  padding: 14px 32px;
  border-radius: 50px;
  font-weight: 600;
  font-size: 15px;
  transition: all 0.3s ease;
}

.hotel-location .location-content .action-buttons .btn.btn-primary {
  background: var(--accent-color);
  color: var(--contrast-color);
  border: 2px solid var(--accent-color);
}

.hotel-location .location-content .action-buttons .btn.btn-primary:hover {
  background: transparent;
  color: var(--accent-color);
  transform: translateY(-2px);
}

.hotel-location .location-content .action-buttons .btn.btn-outline {
  background: transparent;
  color: var(--heading-color);
  border: 2px solid color-mix(in srgb, var(--default-color), transparent 80%);
}

.hotel-location .location-content .action-buttons .btn.btn-outline:hover {
  background: var(--heading-color);
  color: var(--contrast-color);
  border-color: var(--heading-color);
  transform: translateY(-2px);
}

.hotel-location .map-container {
  position: relative;
  height: 500px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 15px 50px color-mix(in srgb, var(--default-color), transparent 85%);
}

.hotel-location .map-container iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.hotel-location .map-container .map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.hotel-location .map-container .hotel-marker {
  position: absolute;
  top: 30px;
  right: 30px;
  background: var(--accent-color);
  color: var(--contrast-color);
  padding: 12px 20px;
  border-radius: 15px;
  font-weight: 600;
  box-shadow: 0 8px 25px color-mix(in srgb, var(--accent-color), transparent 50%);
  display: flex;
  align-items: center;
  gap: 10px;
}

.hotel-location .map-container .hotel-marker i {
  font-size: 18px;
}

.hotel-location .transportation-hub {
  background: color-mix(in srgb, var(--surface-color), transparent 50%);
  border-radius: 25px;
  padding: 60px 40px;
  margin: 80px 0;
}

.hotel-location .transportation-hub .section-header {
  text-align: center;
  margin-bottom: 50px;
}

.hotel-location .transportation-hub .section-header h3 {
  color: var(--heading-color);
  font-size: 36px;
  margin-bottom: 15px;
}

.hotel-location .transportation-hub .section-header p {
  color: color-mix(in srgb, var(--default-color), transparent 35%);
  font-size: 16px;
  margin: 0;
}

.hotel-location .transportation-hub .transport-card {
  background: var(--surface-color);
  border-radius: 20px;
  padding: 30px;
  height: 100%;
  box-shadow: 0 8px 30px color-mix(in srgb, var(--default-color), transparent 90%);
  transition: all 0.3s ease;
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 95%);
}

.hotel-location .transportation-hub .transport-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px color-mix(in srgb, var(--default-color), transparent 80%);
}

.hotel-location .transportation-hub .transport-card .transport-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
}

.hotel-location .transportation-hub .transport-card .transport-header .transport-icon-box {
  background: color-mix(in srgb, var(--accent-color), transparent 88%);
  width: 60px;
  height: 60px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.hotel-location .transportation-hub .transport-card .transport-header .transport-icon-box i {
  color: var(--accent-color);
  font-size: 24px;
}

.hotel-location .transportation-hub .transport-card .transport-header .transport-title h5 {
  color: var(--heading-color);
  font-size: 20px;
  margin-bottom: 5px;
}

.hotel-location .transportation-hub .transport-card .transport-header .transport-title .transport-time {
  color: var(--accent-color);
  font-size: 14px;
  font-weight: 500;
}

.hotel-location .transportation-hub .transport-card .transport-details p {
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  line-height: 1.6;
  margin-bottom: 20px;
}

.hotel-location .transportation-hub .transport-card .transport-details .transport-features {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.hotel-location .transportation-hub .transport-card .transport-details .transport-features .feature-tag {
  background: color-mix(in srgb, var(--accent-color), transparent 92%);
  color: var(--accent-color);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.hotel-location .attractions-section .section-header {
  text-align: center;
  margin-bottom: 60px;
}

.hotel-location .attractions-section .section-header h3 {
  color: var(--heading-color);
  font-size: 36px;
  margin-bottom: 15px;
}

.hotel-location .attractions-section .section-header p {
  color: color-mix(in srgb, var(--default-color), transparent 35%);
  font-size: 16px;
  margin: 0;
}

.hotel-location .attractions-section .attraction-item {
  background: var(--surface-color);
  border-radius: 18px;
  overflow: hidden;
  margin-bottom: 30px;
  box-shadow: 0 10px 40px color-mix(in srgb, var(--default-color), transparent 88%);
  transition: all 0.3s ease;
}

.hotel-location .attractions-section .attraction-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 25px 60px color-mix(in srgb, var(--default-color), transparent 80%);
}

.hotel-location .attractions-section .attraction-item:hover .attraction-photo img {
  transform: scale(1.05);
}

.hotel-location .attractions-section .attraction-item .attraction-photo {
  position: relative;
  height: 220px;
  overflow: hidden;
}

.hotel-location .attractions-section .attraction-item .attraction-photo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.hotel-location .attractions-section .attraction-item .attraction-photo .distance-badge {
  position: absolute;
  top: 15px;
  left: 15px;
  background: var(--contrast-color);
  color: var(--accent-color);
  padding: 8px 15px;
  border-radius: 25px;
  font-size: 13px;
  font-weight: 600;
  box-shadow: 0 4px 15px color-mix(in srgb, var(--default-color), transparent 85%);
  display: flex;
  align-items: center;
  gap: 5px;
}

.hotel-location .attractions-section .attraction-item .attraction-photo .distance-badge i {
  font-size: 12px;
}

.hotel-location .attractions-section .attraction-item .attraction-info {
  padding: 25px;
}

.hotel-location .attractions-section .attraction-item .attraction-info h5 {
  color: var(--heading-color);
  font-size: 22px;
  margin-bottom: 10px;
}

.hotel-location .attractions-section .attraction-item .attraction-info .walk-time {
  color: var(--accent-color);
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.hotel-location .attractions-section .attraction-item .attraction-info .walk-time i {
  font-size: 13px;
}

.hotel-location .attractions-section .attraction-item .attraction-info p:last-child {
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  line-height: 1.6;
  margin: 0;
  font-size: 15px;
}

@media (max-width: 992px) {
  .hotel-location .location-content {
    padding: 30px 0;
  }

  .hotel-location .location-content .location-title {
    font-size: 36px;
  }

  .hotel-location .location-content .info-grid {
    grid-template-columns: 1fr;
    gap: 25px;
  }

  .hotel-location .map-container {
    height: 400px;
    margin-bottom: 40px;
  }

  .hotel-location .transportation-hub {
    padding: 50px 30px;
    margin: 60px 0;
  }
}

@media (max-width: 768px) {
  .hotel-location .location-content .location-title {
    font-size: 32px;
  }

  .hotel-location .location-content .action-buttons {
    flex-direction: column;
  }

  .hotel-location .location-content .action-buttons .btn {
    text-align: center;
  }

  .hotel-location .map-container {
    height: 350px;
  }

  .hotel-location .map-container .hotel-marker {
    top: 20px;
    right: 20px;
    padding: 10px 16px;
    font-size: 14px;
  }

  .hotel-location .transportation-hub {
    padding: 40px 20px;
  }

  .hotel-location .transportation-hub .section-header h3 {
    font-size: 28px;
  }

  .hotel-location .transportation-hub .transport-card {
    padding: 25px;
    margin-bottom: 20px;
  }

  .hotel-location .transportation-hub .transport-card .transport-header {
    gap: 15px;
  }

  .hotel-location .transportation-hub .transport-card .transport-header .transport-icon-box {
    width: 50px;
    height: 50px;
  }

  .hotel-location .transportation-hub .transport-card .transport-header .transport-icon-box i {
    font-size: 20px;
  }

  .hotel-location .attractions-section .section-header h3 {
    font-size: 28px;
  }

  .hotel-location .attractions-section .attraction-item .attraction-photo {
    height: 200px;
  }

  .hotel-location .attractions-section .attraction-item .attraction-info {
    padding: 20px;
  }
}

@media (max-width: 576px) {
  .hotel-location .transportation-hub .transport-card .transport-header {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }
}

/*--------------------------------------------------------------
# Contact Section
--------------------------------------------------------------*/
.contact .contact-form-wrapper {
  background-color: var(--surface-color);
  border-radius: 10px;
  padding: 40px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.06);
  margin-bottom: 50px;
}

.contact .contact-form-wrapper h2 {
  font-size: 28px;
  font-weight: 700;
  color: var(--heading-color);
  margin-bottom: 30px;
  position: relative;
  padding-bottom: 15px;
  text-align: center;
}

.contact .contact-form-wrapper h2:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: var(--accent-color);
}

.contact .contact-form-wrapper .form-control {
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 80%);
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 15px;
  background-color: var(--surface-color);
  color: var(--default-color);
  transition: 0.3s;
  margin-bottom: 15px;
}

.contact .contact-form-wrapper .form-control:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px color-mix(in srgb, var(--accent-color), transparent 90%);
}

.contact .contact-form-wrapper .form-control::placeholder {
  color: color-mix(in srgb, var(--default-color), transparent 50%);
}

.contact .contact-form-wrapper textarea.form-control {
  resize: vertical;
  min-height: 180px;
}

.contact .contact-form-wrapper .btn-submit {
  background-color: var(--accent-color);
  border: none;
  color: var(--contrast-color);
  padding: 13px 35px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  transition: 0.3s;
  display: inline-block;
  cursor: pointer;
}

.contact .contact-form-wrapper .btn-submit:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 20%);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px color-mix(in srgb, var(--accent-color), transparent 70%);
}

.contact .contact-info-card {
  background-color: color-mix(in srgb, var(--accent-color), transparent 92%);
  border-radius: 8px;
  padding: 30px;
  display: flex;
  align-items: flex-start;
  gap: 20px;
  transition: 0.3s;
  height: 100%;
}

.contact .contact-info-card:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 85%);
  transform: translateY(-5px);
}

.contact .contact-info-card .icon-box {
  width: 60px;
  height: 60px;
  border-radius: 10px;
  background-color: var(--accent-color);
  color: var(--contrast-color);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.contact .contact-info-card .icon-box i {
  font-size: 26px;
}

.contact .contact-info-card .info-content h4 {
  font-size: 19px;
  font-weight: 700;
  margin-bottom: 12px;
  color: var(--heading-color);
}

.contact .contact-info-card .info-content p {
  margin-bottom: 8px;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  font-size: 16px;
}

.contact .map-container {
  position: relative;
  overflow: hidden;
  border-radius: 15px;
  margin-top: 50px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
}

.contact .map-container iframe {
  width: 100%;
  height: 500px;
  border: none;
  display: block;
}

@media (max-width: 768px) {
  .contact .contact-form-wrapper {
    padding: 30px;
  }

  .contact .contact-form-wrapper h2 {
    font-size: 24px;
    margin-bottom: 25px;
  }

  .contact .contact-info-card {
    padding: 25px;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .contact .contact-info-card .icon-box {
    margin-bottom: 15px;
  }

  .contact .map-container iframe {
    height: 400px;
  }
}

@media (max-width: 576px) {
  .contact .contact-form-wrapper {
    padding: 20px;
  }

  .contact .contact-form-wrapper h2 {
    font-size: 22px;
  }

  .contact .contact-info-card {
    padding: 20px;
  }

  .contact .map-container iframe {
    height: 350px;
  }
}

/*--------------------------------------------------------------
# Terms Of Service Section
--------------------------------------------------------------*/
.terms-of-service .tos-header {
  margin-bottom: 60px;
}

.terms-of-service .tos-header .last-updated {
  display: inline-block;
  padding: 8px 20px;
  background-color: color-mix(in srgb, var(--accent-color), transparent 90%);
  border-radius: 30px;
  color: var(--accent-color);
  font-size: 0.95rem;
  margin-bottom: 20px;
}

.terms-of-service .tos-header h2 {
  font-size: 2.5rem;
  margin-bottom: 15px;
}

.terms-of-service .tos-header p {
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  font-size: 1.1rem;
  max-width: 700px;
  margin: 0 auto;
}

.terms-of-service .tos-content .content-section {
  margin-bottom: 50px;
  scroll-margin-top: 100px;
}

.terms-of-service .tos-content .content-section:last-child {
  margin-bottom: 0;
}

.terms-of-service .tos-content .content-section h3 {
  font-size: 1.8rem;
  margin-bottom: 20px;
  color: var(--heading-color);
}

.terms-of-service .tos-content .content-section p {
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  line-height: 1.7;
  margin-bottom: 20px;
}

.terms-of-service .tos-content .content-section p:last-child {
  margin-bottom: 0;
}

.terms-of-service .tos-content .content-section .info-box {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 20px;
  background-color: color-mix(in srgb, var(--accent-color), transparent 95%);
  border-radius: 15px;
  margin-top: 20px;
}

.terms-of-service .tos-content .content-section .info-box i {
  font-size: 1.5rem;
  color: var(--accent-color);
  flex-shrink: 0;
}

.terms-of-service .tos-content .content-section .info-box p {
  margin: 0;
  font-size: 0.95rem;
}

.terms-of-service .tos-content .content-section .list-items {
  list-style: none;
  padding: 0;
  margin: 20px 0;
}

.terms-of-service .tos-content .content-section .list-items li {
  position: relative;
  padding-left: 25px;
  margin-bottom: 12px;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
}

.terms-of-service .tos-content .content-section .list-items li:last-child {
  margin-bottom: 0;
}

.terms-of-service .tos-content .content-section .list-items li::before {
  content: "";
  position: absolute;
  left: 0;
  top: 10px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--accent-color);
}

.terms-of-service .tos-content .content-section .alert-box {
  display: flex;
  gap: 20px;
  padding: 25px;
  background-color: var(--surface-color);
  border-radius: 15px;
  border-left: 4px solid var(--accent-color);
  margin-top: 20px;
}

.terms-of-service .tos-content .content-section .alert-box i {
  font-size: 2rem;
  color: var(--accent-color);
  flex-shrink: 0;
}

.terms-of-service .tos-content .content-section .alert-box .alert-content h5 {
  font-size: 1.1rem;
  margin-bottom: 8px;
}

.terms-of-service .tos-content .content-section .alert-box .alert-content p {
  margin: 0;
  font-size: 0.95rem;
}

.terms-of-service .tos-content .content-section .prohibited-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-top: 20px;
}

@media (max-width: 576px) {
  .terms-of-service .tos-content .content-section .prohibited-list {
    grid-template-columns: 1fr;
  }
}

.terms-of-service .tos-content .content-section .prohibited-list .prohibited-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  background-color: var(--surface-color);
  border-radius: 12px;
}

.terms-of-service .tos-content .content-section .prohibited-list .prohibited-item i {
  color: #dc3545;
  font-size: 1.2rem;
}

.terms-of-service .tos-content .content-section .prohibited-list .prohibited-item span {
  font-size: 0.95rem;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
}

.terms-of-service .tos-content .content-section .disclaimer-box {
  background-color: var(--surface-color);
  padding: 25px;
  border-radius: 15px;
  margin-top: 20px;
}

.terms-of-service .tos-content .content-section .disclaimer-box p {
  margin-bottom: 15px;
  font-weight: 500;
}

.terms-of-service .tos-content .content-section .disclaimer-box ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.terms-of-service .tos-content .content-section .disclaimer-box ul li {
  position: relative;
  padding-left: 25px;
  margin-bottom: 12px;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  font-size: 0.95rem;
}

.terms-of-service .tos-content .content-section .disclaimer-box ul li:last-child {
  margin-bottom: 0;
}

.terms-of-service .tos-content .content-section .disclaimer-box ul li::before {
  content: "•";
  position: absolute;
  left: 8px;
  color: var(--accent-color);
}

.terms-of-service .tos-content .content-section .notice-box {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background-color: color-mix(in srgb, var(--accent-color), transparent 95%);
  border-radius: 15px;
  margin-top: 20px;
}

.terms-of-service .tos-content .content-section .notice-box i {
  font-size: 1.5rem;
  color: var(--accent-color);
  flex-shrink: 0;
}

.terms-of-service .tos-content .content-section .notice-box p {
  margin: 0;
  font-size: 0.95rem;
}

.terms-of-service .tos-contact {
  margin-top: 60px;
}

.terms-of-service .tos-contact .contact-box {
  background: linear-gradient(135deg, color-mix(in srgb, var(--accent-color), transparent 95%) 0%, color-mix(in srgb, var(--accent-color), transparent 98%) 100%);
  border-radius: 20px;
  padding: 40px;
  display: flex;
  align-items: center;
  gap: 30px;
}

@media (max-width: 576px) {
  .terms-of-service .tos-contact .contact-box {
    flex-direction: column;
    text-align: center;
  }
}

.terms-of-service .tos-contact .contact-box .contact-icon {
  width: 60px;
  height: 60px;
  background-color: var(--accent-color);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.terms-of-service .tos-contact .contact-box .contact-icon i {
  font-size: 1.8rem;
  color: var(--contrast-color);
}

.terms-of-service .tos-contact .contact-box .contact-content {
  flex: 1;
}

.terms-of-service .tos-contact .contact-box .contact-content h4 {
  font-size: 1.4rem;
  margin-bottom: 8px;
}

.terms-of-service .tos-contact .contact-box .contact-content p {
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  margin-bottom: 15px;
}

.terms-of-service .tos-contact .contact-box .contact-content .contact-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 25px;
  background-color: var(--accent-color);
  color: var(--contrast-color);
  border-radius: 30px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s;
}

.terms-of-service .tos-contact .contact-box .contact-content .contact-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

@media print {
  .terms-of-service .tos-contact {
    display: none;
  }

  .terms-of-service .content-section {
    page-break-inside: avoid;
  }
}

/*--------------------------------------------------------------
# Privacy Section
--------------------------------------------------------------*/
.privacy {
  font-size: 1rem;
  line-height: 1.7;
}

.privacy .privacy-header {
  margin-bottom: 60px;
  text-align: center;
  border-bottom: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
  padding-bottom: 40px;
}

.privacy .privacy-header .header-content .last-updated {
  font-size: 0.95rem;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  margin-bottom: 20px;
}

.privacy .privacy-header .header-content h1 {
  font-size: 2.8rem;
  color: var(--heading-color);
  margin-bottom: 20px;
  font-weight: 600;
}

.privacy .privacy-header .header-content .intro-text {
  font-size: 1.2rem;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  line-height: 1.6;
}

.privacy .privacy-content .content-section {
  margin-bottom: 50px;
}

.privacy .privacy-content .content-section:last-child {
  margin-bottom: 0;
}

.privacy .privacy-content .content-section h2 {
  font-size: 1.8rem;
  color: var(--heading-color);
  margin-bottom: 25px;
  font-weight: 600;
}

.privacy .privacy-content .content-section h3 {
  font-size: 1.4rem;
  color: var(--heading-color);
  margin: 30px 0 20px;
  font-weight: 500;
}

.privacy .privacy-content .content-section p {
  margin-bottom: 20px;
}

.privacy .privacy-content .content-section p:last-child {
  margin-bottom: 0;
}

.privacy .privacy-content .content-section ul {
  list-style: none;
  padding: 0;
  margin: 0 0 20px;
}

.privacy .privacy-content .content-section ul li {
  position: relative;
  padding-left: 25px;
  margin-bottom: 12px;
}

.privacy .privacy-content .content-section ul li:last-child {
  margin-bottom: 0;
}

.privacy .privacy-content .content-section ul li::before {
  content: "•";
  position: absolute;
  left: 8px;
  color: var(--accent-color);
}

.privacy .privacy-contact {
  max-width: 800px;
  margin: 0 auto;
  padding-top: 40px;
  border-top: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
}

.privacy .privacy-contact h2 {
  font-size: 1.8rem;
  color: var(--heading-color);
  margin-bottom: 20px;
  font-weight: 600;
}

.privacy .privacy-contact p {
  margin-bottom: 20px;
}

.privacy .privacy-contact .contact-details {
  background-color: var(--surface-color);
  padding: 25px;
  border-radius: 10px;
}

.privacy .privacy-contact .contact-details p {
  margin-bottom: 10px;
}

.privacy .privacy-contact .contact-details p:last-child {
  margin-bottom: 0;
}

.privacy .privacy-contact .contact-details p strong {
  color: var(--heading-color);
  font-weight: 600;
}

@media print {
  .privacy {
    font-size: 12pt;
    line-height: 1.5;
  }

  .privacy .privacy-header {
    text-align: left;
    border-bottom: 1pt solid #000;
    padding-bottom: 20pt;
    margin-bottom: 30pt;
  }

  .privacy h1 {
    font-size: 24pt;
  }

  .privacy h2 {
    font-size: 18pt;
    page-break-after: avoid;
  }

  .privacy h3 {
    font-size: 14pt;
    page-break-after: avoid;
  }

  .privacy p,
  .privacy ul {
    page-break-inside: avoid;
  }

  .privacy .contact-details {
    border: 1pt solid #000;
    padding: 15pt;
  }
}

@media (max-width: 767px) {
  .privacy .privacy-header {
    margin-bottom: 40px;
    padding-bottom: 30px;
  }

  .privacy .privacy-header .header-content h1 {
    font-size: 2.2rem;
  }

  .privacy .privacy-header .header-content .intro-text {
    font-size: 1.1rem;
  }

  .privacy .privacy-content .content-section {
    margin-bottom: 40px;
  }

  .privacy .privacy-content .content-section h2 {
    font-size: 1.6rem;
  }

  .privacy .privacy-content .content-section h3 {
    font-size: 1.3rem;
  }
}

/*--------------------------------------------------------------
# Error 404 Section
--------------------------------------------------------------*/
.error-404 {
  padding: 80px 0;
  margin: 0 auto;
}

.error-404 .error-icon {
  font-size: 5rem;
  color: color-mix(in srgb, var(--accent-color), transparent 15%);
}

.error-404 .error-code {
  font-size: clamp(6rem, 15vw, 12rem);
  font-weight: 800;
  color: color-mix(in srgb, var(--heading-color), transparent 10%);
  font-family: var(--heading-font);
  line-height: 1;
}

.error-404 .error-title {
  font-size: 2rem;
  color: var(--heading-color);
  font-weight: 600;
}

.error-404 .error-text {
  font-size: 1.1rem;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  max-width: 600px;
  margin: 0 auto;
}

.error-404 .search-box {
  max-width: 500px;
  margin: 0 auto;
}

.error-404 .search-box .input-group {
  border-radius: 50px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.error-404 .search-box .form-control {
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  color: var(--default-color);
  background-color: var(--surface-color);
  border-radius: 50px;
}

.error-404 .search-box .form-control:focus {
  box-shadow: none;
  border-color: var(--accent-color);
}

.error-404 .search-box .form-control::placeholder {
  color: color-mix(in srgb, var(--default-color), transparent 60%);
}

.error-404 .search-box .search-btn {
  background-color: var(--accent-color);
  color: var(--contrast-color);
  border: none;
  padding: 0.75rem 1.5rem;
  transition: all 0.3s ease;
}

.error-404 .search-box .search-btn:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 15%);
}

.error-404 .error-action .btn-primary {
  padding: 0.75rem 2rem;
  font-size: 1.1rem;
  background-color: var(--accent-color);
  border: none;
  color: var(--contrast-color);
  border-radius: 50px;
  transition: all 0.3s ease;
}

.error-404 .error-action .btn-primary:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 15%);
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .error-404 {
    padding: 60px 0;
  }

  .error-404 .error-code {
    font-size: clamp(4rem, 12vw, 8rem);
  }

  .error-404 .error-title {
    font-size: 1.5rem;
  }

  .error-404 .error-text {
    font-size: 1rem;
    padding: 0 20px;
  }

  .error-404 .search-box {
    margin: 0 20px;
  }
}

/*--------------------------------------------------------------
# Starter Section Section
--------------------------------------------------------------*/
.starter-section {
  /* Add your styles here */
}